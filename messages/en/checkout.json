{"checkout": {"title": "Checkout", "shippingAddress": "Shipping Address", "selectShippingAddress": "Select your shipping address", "selectAddress": "Select an address", "selectAddressError": "Please select a shipping address", "emptyCartError": "Your cart is empty", "orderError": "Failed to place order. Please try again.", "noAddressesFound": "No addresses found", "addAddressInProfile": "Please add an address in your profile first", "addNewAddress": "Add New Address", "useThisAddress": "Use This Address", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "processing": "Processing...", "placingOrder": "Placing order...", "orderSuccess": "Order Placed Successfully!", "orderPlaced": "Order Placed Successfully!", "orderNumber": "Order Number", "thankYou": "Thank you for your order", "paymentInstructions": "Payment Instructions", "paymentInstructionsText": "Please transfer the total amount to our bank account. Your order will be processed once we confirm your payment.", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2 (Optional)", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "saveAddress": "Save Address", "setAsDefault": "Set as default address", "items": "Items", "total": "Total", "subtotal": "Subtotal", "shipping": "Shipping", "bankTransferDetails": "Bank Transfer Details", "bankName": "Bank Name", "accountName": "Account Name", "accountNumber": "Account Number", "swiftCode": "SWIFT Code", "reference": "Reference", "paymentNote": "Please include your order number in the payment reference for faster processing.", "bankNameValue": "Example Bank", "accountNameValue": "MaoMao Trading Co.", "accountNumberValue": "**********", "swiftCodeValue": "EXAMPLSW", "whatNext": "What happens next?", "step1Title": "Payment Confirmation", "step1Description": "We'll send you payment instructions via email", "step2Title": "Order Processing", "step2Description": "Once payment is confirmed, we'll start processing your order", "step3Title": "Shipping", "step3Description": "Your order will be shipped and you'll receive tracking information", "viewOrder": "View Order Details", "continueShopping": "Continue Shopping"}}