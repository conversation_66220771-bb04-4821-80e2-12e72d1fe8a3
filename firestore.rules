rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Cart collection rules
    // Users can only access their own cart documents where the document ID equals their UID
    match /carts/{userId} {
      // Allow read, write, update, delete only if the document ID matches the authenticated user's UID
      allow read, write, update, delete: if request.auth != null && request.auth.uid == userId;

      // Additional validation for cart data structure
      allow create, update: if request.auth != null &&
        request.auth.uid == userId &&
        // Validate cart data structure
        request.resource.data.keys().hasAll(['userId', 'items', 'currency', 'totals', 'itemCount', 'updatedAt']) &&
        // Ensure userId matches document ID
        request.resource.data.userId == userId &&
        // Validate currency is a string
        request.resource.data.currency is string &&
        // Validate itemCount is a number
        request.resource.data.itemCount is number &&
        // Validate totals structure
        request.resource.data.totals.keys().hasAll(['subtotal', 'total']) &&
        request.resource.data.totals.subtotal is number &&
        request.resource.data.totals.total is number &&
        // Validate items is an array
        request.resource.data.items is list &&
        // Validate each item in the array
        request.resource.data.items.size() == 0 || (
          request.resource.data.items[0].keys().hasAll(['productId', 'quantity', 'price', 'currency', 'productName', 'productSlug']) &&
          request.resource.data.items[0].productId is string &&
          request.resource.data.items[0].quantity is number &&
          request.resource.data.items[0].price is number &&
          request.resource.data.items[0].currency is string &&
          request.resource.data.items[0].productName is string &&
          request.resource.data.items[0].productSlug is string
        );
    }

    // Users collection - for storing user preferences like currency
    match /users/{userId} {
      // Only authenticated users can access their own user document
      allow read, write, update: if request.auth != null && request.auth.uid == userId;

      // Allow create only for authenticated users creating their own document
      allow create: if request.auth != null && request.auth.uid == userId;
    }

    // Deny all other access by default
    match /{document=**} {
      allow read, write, update, delete: if false;
    }
  }
}