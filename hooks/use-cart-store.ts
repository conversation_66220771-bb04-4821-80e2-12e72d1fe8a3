'use client';

// hooks/use-cart-store.ts
// Zustand store for shopping cart with Firestore persistence

import { create } from 'zustand';
import type { CartData, CartState } from '@/lib/types';
import { getCart as fetchCart, addToCart as serverAddToCart, updateCartQuantity, removeFromCart as serverRemoveFromCart, clearCart as serverClearCart } from '@/lib/actions/cart.actions';

interface CartStore extends CartState {
  // Internal state
  isInitialized: boolean;
  initialize: () => Promise<void>;
}

export const useCartStore = create<CartStore>((set, get) => ({
  // Initial state
  data: null,
  isLoading: false,
  error: null,
  isInitialized: false,

  // Initialize cart from server
  initialize: async () => {
    if (get().isInitialized) return;

    set({ isLoading: true, error: null });

    try {
      const cartData = await fetchCart();

      set({
        data: cartData,
        isLoading: false,
        isInitialized: true,
      });
    } catch (error) {
      console.error('Failed to initialize cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to load cart',
        isLoading: false,
        isInitialized: true,
      });
    }
  },

  // Refresh cart data from server
  refreshCart: async () => {
    set({ isLoading: true, error: null });

    try {
      const cartData = await fetchCart();
      set({ data: cartData, isLoading: false });
    } catch (error) {
      console.error('Failed to refresh cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to refresh cart',
        isLoading: false,
      });
    }
  },

  // Add item to cart (server-side)
  addItem: async (item) => {
    set({ isLoading: true, error: null });

    try {
      const result = await serverAddToCart({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
      });

      if (result.success) {
        // Refresh cart data after successful addition
        await get().refreshCart();
      } else {
        set({
          error: result.error || 'Failed to add item to cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to add item to cart',
        isLoading: false,
      });
    }
  },

  // Remove item from cart (server-side)
  removeItem: async (productId, variantId) => {
    set({ isLoading: true, error: null });

    try {
      const result = await serverRemoveFromCart({ productId, variantId });

      if (result.success) {
        await get().refreshCart();
      } else {
        set({
          error: result.error || 'Failed to remove item from cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to remove item from cart',
        isLoading: false,
      });
    }
  },

  // Update item quantity (server-side)
  updateQuantity: async (productId, quantity, variantId) => {
    set({ isLoading: true, error: null });

    try {
      const result = await updateCartQuantity({ productId, quantity, variantId });

      if (result.success) {
        await get().refreshCart();
      } else {
        set({
          error: result.error || 'Failed to update item quantity',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to update item quantity:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to update item quantity',
        isLoading: false,
      });
    }
  },

  // Clear cart (server-side)
  clearCart: async () => {
    set({ isLoading: true, error: null });

    try {
      const result = await serverClearCart();

      if (result.success) {
        set({
          data: {
            items: [],
            totals: { subtotal: 0, total: 0, currency: 'USD' },
            itemCount: 0,
          },
          isLoading: false,
        });
      } else {
        set({
          error: result.error || 'Failed to clear cart',
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Failed to clear cart:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to clear cart',
        isLoading: false,
      });
    }
  },
}));
