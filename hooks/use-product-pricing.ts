'use client';

import { useMemo } from 'react';
import type { ProductWithDetails } from '@/lib/types';

// Extended types that include server-calculated display properties
export type OfferWithDisplay = ProductWithDetails['offers'][0] & {
  display_price?: number;
  display_currency?: string;
  exchange_rate?: number;
};

export type VariantWithDisplay = ProductWithDetails['variants'][0] & {
  display_price?: number;
  display_currency?: string;
  exchange_rate?: number;
};

interface UseProductPricingProps {
  product: ProductWithDetails;
  selectedVariant?: number | undefined;
  quantity?: number;
}

interface ProductPricingResult {
  price: number;
  currency: string;
  currentOffer: OfferWithDisplay;
  variantTypes: string[];
  getOfferForQuantity: (qty: number) => OfferWithDisplay;
}

export function useProductPricing({
  product,
  selectedVariant,
  quantity = 1
}: UseProductPricingProps): ProductPricingResult {
  // Get unique variant types from variants for dynamic label
  const variantTypes = useMemo(() => {
    return Array.from(new Set(product.variants.map(variant => {
      const variantTranslation = variant.translations[0];
      return variantTranslation?.variant_type || variant.original_variant_type;
    })));
  }, [product.variants]);

  // Find the appropriate offer based on quantity
  const getOfferForQuantity = useMemo(() => {
    return (qty: number): OfferWithDisplay => {
      // Sort offers by min_quantity ascending
      const sortedOffers = [...product.offers].sort((a, b) =>
        Number(a.min_quantity || 0) - Number(b.min_quantity || 0)
      );

      // Find the best offer (highest min_quantity that still qualifies)
      let bestOffer = sortedOffers[0] as OfferWithDisplay; // fallback to first

      for (const offer of sortedOffers) {
        const minQty = Number(offer.min_quantity || 1);
        if (qty >= minQty) {
          bestOffer = offer as OfferWithDisplay;
        } else {
          // Since offers are sorted, we can break early
          break;
        }
      }

      return bestOffer;
    };
  }, [product.offers]);

  const currentOffer = getOfferForQuantity(quantity);

  // Calculate price and currency
  const { price, currency } = useMemo(() => {
    if (selectedVariant !== undefined) {
      // When variant is selected, get the variant base price and apply quantity discounts
      const selectedVariantData = product.variants.find((v, idx) => (v.id || idx) === selectedVariant) as VariantWithDisplay;
      const variantPrice = selectedVariantData ? Number(selectedVariantData.display_price || selectedVariantData.price_low) : 0;
      const variantCurrency = selectedVariantData?.display_currency || selectedVariantData?.currency || "Error";

      // Apply quantity discount to variant price
      // Get the base offer (usually the first/highest price offer) to calculate discount ratio
      const baseOffer = product.offers[0] as OfferWithDisplay;
      const baseOfferPrice = baseOffer ? Number(baseOffer.display_price || baseOffer.price_low) : variantPrice;
      const currentOfferPrice = currentOffer ? Number(currentOffer.display_price || currentOffer.price_low) : variantPrice;

      // Calculate discount ratio from base offer to current offer
      const discountRatio = baseOfferPrice > 0 ? currentOfferPrice / baseOfferPrice : 1;

      // Apply the same discount ratio to the variant price
      const discountedVariantPrice = variantPrice * discountRatio;

      return { price: discountedVariantPrice, currency: variantCurrency };
    } else {
      // No variant selected - use offer price directly
      const offerPrice = currentOffer ? Number(currentOffer.display_price || currentOffer.price_low) : 0;
      const offerCurrency = currentOffer?.display_currency || currentOffer?.currency || "Error";
      return { price: offerPrice, currency: offerCurrency };
    }
  }, [selectedVariant, product.variants, product.offers, currentOffer]);

  return {
    price,
    currency,
    currentOffer,
    variantTypes,
    getOfferForQuantity
  };
}