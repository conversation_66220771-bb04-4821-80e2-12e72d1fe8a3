// app/[locale]/admin/categories/[categoryId]/page.tsx
// Admin category detail page

import { notFound, redirect } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { ArrowLeft, Edit, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getCategoryById } from '@/lib/actions/admin/category.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { PermissionAction } from '@/app/generated/prisma';
import { CategoryDeleteButton } from '@/components/admin/CategoryDeleteButton';

interface PageProps {
  params: Promise<{ locale: string; categoryId: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, categoryId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  const category = await getCategoryById(parseInt(categoryId));
  
  if ('error' in category) {
    return {
      title: t('categoryNotFound'),
    };
  }

  const translation = category.translations.find(t => t.language_code === locale) || 
                     category.translations.find(t => t.language_code === 'en') ||
                     category.translations[0];

  return {
    title: `${translation?.name || t('category')} - ${t('categoryManagement')}`,
  };
}

export default async function AdminCategoryDetailPage({ params }: PageProps) {
  const { locale, categoryId } = await params;
  const t = await getTranslations({ locale, namespace: 'admin' });

  // Check permissions
  const user = await getCurrentUser();
  if (!user) {
    redirect(`/${locale}/login?redirect=/${locale}/admin/categories/${categoryId}`);
  }

  const canManage = await checkPermission(user.uid, PermissionAction.PRODUCT_MANAGE_CATEGORIES);
  if (!canManage) {
    redirect(`/${locale}/admin/categories`);
  }

  const category = await getCategoryById(parseInt(categoryId));

  if ('error' in category) {
    notFound();
  }

  const translation = category.translations.find(t => t.language_code === locale) || 
                     category.translations.find(t => t.language_code === 'en') ||
                     category.translations[0];

  const categoryName = translation?.name || t('unnamedCategory');

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link href={`/${locale}/admin/categories`}>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('backToCategories')}
        </Button>
      </Link>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{categoryName}</h1>
          <p className="text-muted-foreground mt-2">
            {t('categoryDetails')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/${locale}/admin/categories/${categoryId}/new`}>
            <Button variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              {t('addSubcategory')}
            </Button>
          </Link>
          <Link href={`/${locale}/admin/categories/${categoryId}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              {t('edit')}
            </Button>
          </Link>
          <CategoryDeleteButton
            categoryId={category.id}
            categoryName={categoryName}
            hasChildren={category._count.children > 0}
            hasProducts={category._count.products > 0}
            locale={locale}
          />
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{category._count.products}</p>
              <p className="text-sm text-muted-foreground">{t('products')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{category._count.children}</p>
              <p className="text-sm text-muted-foreground">{t('subcategories')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{category.translations.length}</p>
              <p className="text-sm text-muted-foreground">{t('translations')}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold">
                {category.parent_id ? t('subcategory') : t('rootCategory')}
              </p>
              <p className="text-sm text-muted-foreground">{t('type')}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Category Details */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">{t('overview')}</TabsTrigger>
          <TabsTrigger value="translations">{t('translations')}</TabsTrigger>
          <TabsTrigger value="hierarchy">{t('hierarchy')}</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>{t('basicInformation')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('categoryId')}</p>
                  <p className="font-mono">{category.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('name')}</p>
                  <p>{categoryName}</p>
                </div>
                {translation?.description && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{t('description')}</p>
                    <p className="text-sm">{translation.description}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{t('slug')}</p>
                  <p className="font-mono text-sm">{translation?.slug}</p>
                </div>
              </CardContent>
            </Card>

            {/* Parent Category */}
            {category.parent && (
              <Card>
                <CardHeader>
                  <CardTitle>{t('parentCategory')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">
                        {category.parent.translations.find(t => t.language_code === locale)?.name ||
                         category.parent.translations[0]?.name ||
                         `Category ${category.parent.id}`}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        ID: {category.parent.id}
                      </p>
                    </div>
                    <Link href={`/${locale}/admin/categories/${category.parent.id}`}>
                      <Button variant="outline" size="sm">
                        {t('view')}
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Translations Tab */}
        <TabsContent value="translations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('categoryTranslations')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {category.translations.map((translation) => (
                  <div key={translation.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{translation.language_code}</Badge>
                          <h4 className="font-medium">{translation.name}</h4>
                        </div>
                        <p className="text-sm text-muted-foreground font-mono">
                          /{translation.slug}
                        </p>
                        {translation.description && (
                          <p className="text-sm">{translation.description}</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Hierarchy Tab */}
        <TabsContent value="hierarchy" className="space-y-6">
          {/* Subcategories */}
          {category.children.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{t('subcategories')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {category.children.map((child) => {
                    const childTranslation = child.translations.find(t => t.language_code === locale) ||
                                           child.translations[0];
                    return (
                      <div key={child.id} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">{childTranslation?.name || `Category ${child.id}`}</p>
                          <p className="text-sm text-muted-foreground">ID: {child.id}</p>
                        </div>
                        <Link href={`/${locale}/admin/categories/${child.id}`}>
                          <Button variant="outline" size="sm">
                            {t('view')}
                          </Button>
                        </Link>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
