// app/[locale]/(marketing)/products/[slug]/page.tsx
// Product detail page with ISR for improved performance

export const dynamicParams = true;

// Generate static params for all locale/product combinations
export async function generateStaticParams() {
  const { LOCALES } = await import('@/lib/constants');
  const { getAllProductSlugsForStaticGeneration } = await import('@/lib/utils/static-generation');

  const params = [];

  for (const locale of LOCALES) {
    const slugs = await getAllProductSlugsForStaticGeneration(locale);
    for (const slug of slugs) {
      params.push({ locale, slug });
    }
  }

  return params;
}

import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { getProductBySlug, getRelatedProducts } from '@/lib/actions/product.actions';
import { ProductDetailClient } from '@/components/products/ProductDetailClient';
import { RelatedProductsClient } from '@/components/products/RelatedProductsClient';
import { DEFAULT_CURRENCY } from '@/lib/constants';

interface PageProps {
  params: Promise<{ locale: string; slug: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { locale, slug } = await params;
  const product = await getProductBySlug(slug, locale);
  const { SITE_CONFIG, LOCALES, MARKETPLACES } = await import('@/lib/constants');

  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  const translation = product.translations[0];
  const productName = translation?.name || product.original_name;

  // Build rich description from product attributes
  const attributes = product.product_attributes.map(attr => {
    const attrTranslation = attr.translations[0];
    const key = attrTranslation?.attr_key || attr.original_attr_key;
    const value = attrTranslation?.attr_value || attr.original_attr_value;
    return `${key}: ${value}`;
  }).slice(0, 3).join(', '); // Limit to first 3 attributes

  const marketplace = MARKETPLACES[product.marketplace];

  // Use currency-neutral description for SEO - users will see localized prices on page load
  const description = attributes
    ? `${productName} - ${attributes}. Available from ${marketplace?.name || product.marketplace}. Authentic Chinese product with worldwide shipping.`
    : `${productName} from ${marketplace?.name || product.marketplace}. Authentic Chinese product with worldwide shipping.`;

  const canonicalUrl = `${SITE_CONFIG.url}/${locale}/products/${slug}`;

  return {
    title: `${productName} - ${marketplace?.name || product.marketplace} | MaoMao`,
    description,
    keywords: [
      productName,
      marketplace?.name || product.marketplace,
      'Chinese products',
      'e-commerce',
      'shopping',
      'international shipping',
      ...product.product_attributes.map(attr => attr.translations[0]?.attr_value || attr.original_attr_value).slice(0, 5)
    ].filter(Boolean),
    alternates: {
      canonical: canonicalUrl,
      languages: LOCALES.reduce((acc, lang) => {
        acc[lang] = `${SITE_CONFIG.url}/${lang}/products/${slug}`;
        return acc;
      }, {} as Record<string, string>),
    },
    openGraph: {
      title: `${productName} - ${marketplace?.name || product.marketplace}`,
      description,
      url: canonicalUrl,
      images: product.product_images.slice(0, 4).map(img => ({
        url: img.image_url,
        alt: `${productName} - ${img.image_type}`,
      })),
    },
    twitter: {
      card: 'summary_large_image',
      title: `${productName} - ${marketplace?.name || product.marketplace}`,
      description,
      images: product.product_images.slice(0, 1).map(img => img.image_url),
    },
  };
}

export default async function ProductDetailPage({ params }: PageProps) {
  const { locale, slug } = await params;
  const t = await getTranslations({ locale, namespace: 'products' });

  // For ISR, generate with default currency - personalization happens client-side
  const defaultCurrency = DEFAULT_CURRENCY;

  // Fetch product with default pricing for static generation
  const product = await getProductBySlug(slug, locale, defaultCurrency);

  if (!product) {
    notFound();
  }

  // Fetch related products with default pricing for static generation
  const relatedProducts = await getRelatedProducts(product.id, locale, 6, defaultCurrency);

  return (
    <div className="container mx-auto px-4 py-8">
      <ProductDetailClient product={product} locale={locale} />

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6">{t('relatedProducts')}</h2>
          <RelatedProductsClient products={relatedProducts} locale={locale} />
        </div>
      )}
    </div>
  );
}
