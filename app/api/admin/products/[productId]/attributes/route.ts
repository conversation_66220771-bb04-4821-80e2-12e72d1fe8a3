// app/api/admin/products/[productId]/attributes/route.ts
// API routes for product attributes management

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/actions/auth.actions';
import { checkPermission } from '@/lib/auth/permissions';
import { PermissionAction } from '@/app/generated/prisma';

// Helper function to serialize BigInts for JSON response
function serializeBigInt<T>(obj: T): T {
  if (typeof obj === 'bigint') {
    return obj.toString() as T;
  } else if (Array.isArray(obj)) {
    return obj.map(serializeBigInt) as T;
  } else if (obj !== null && typeof obj === 'object') {
    const result = {} as Record<string, unknown>;
    for (const key in obj as Record<string, unknown>) {
      result[key] = serializeBigInt((obj as Record<string, unknown>)[key]);
    }
    return result as T;
  }
  return obj;
}

interface RouteParams {
  params: Promise<{ productId: string }>;
}

// GET /api/admin/products/[productId]/attributes - Get all attributes for a product
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_READ
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { productId } = await params;

    const attributes = await prisma.product_attributes.findMany({
      where: { product_id: parseInt(productId) },
      include: {
        translations: true,
      },
      orderBy: { id: 'asc' },
    });

    return NextResponse.json(serializeBigInt(attributes));
  } catch (error) {
    console.error('Error fetching attributes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/admin/products/[productId]/attributes - Create a new attribute for a product
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const hasPermission = await checkPermission(
      user.uid,
      PermissionAction.PRODUCT_UPDATE
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { productId } = await params;
    const { original_attr_key, original_attr_value } = await request.json();

    if (!original_attr_key || !original_attr_value) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const attribute = await prisma.product_attributes.create({
      data: {
        product_id: parseInt(productId),
        original_attr_key,
        original_attr_value,
      },
      include: {
        translations: true,
      },
    });

    return NextResponse.json(serializeBigInt(attribute), { status: 201 });
  } catch (error) {
    console.error('Error creating attribute:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}