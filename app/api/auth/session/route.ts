// app/api/auth/session/route.ts
// API route for managing Firebase session cookies

import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth } from '@/lib/firebase/server';
import { ID_TOKEN_COOKIE_NAME, ID_TOKEN_MAX_AGE } from '@/lib/constants';

/**
 * POST /api/auth/session
 * Creates a session cookie from a Firebase ID token
 */
export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (error) {
      // Handle empty or invalid JSON body
      return NextResponse.json(
        { error: 'Invalid JSON body' },
        { status: 400 }
      );
    }

    const { idToken } = body || {};

    if (!idToken) {
      return NextResponse.json(
        { error: 'ID token is required' },
        { status: 400 }
      );
    }

    // Verify the ID token and create session cookie
    const auth = getAdminAuth();

    // Verify the ID token first
    const decodedToken = await auth.verifyIdToken(idToken);

    // Set the ID token in cookie (1 hour expiration)
    const response = NextResponse.json(
      {
        success: true,
        uid: decodedToken.uid,
      },
      { status: 200 }
    );

    response.cookies.set(ID_TOKEN_COOKIE_NAME, idToken, {
      maxAge: ID_TOKEN_MAX_AGE,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Session creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create session' },
      { status: 401 }
    );
  }
}

/**
 * DELETE /api/auth/session
 * Clears the session cookie (logout)
 */
export async function DELETE() {
  const response = NextResponse.json(
    { success: true },
    { status: 200 }
  );

  // Clear the ID token cookie
  response.cookies.delete(ID_TOKEN_COOKIE_NAME);

  return response;
}
