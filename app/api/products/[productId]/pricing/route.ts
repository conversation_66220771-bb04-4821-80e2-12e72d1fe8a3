// app/api/products/[productId]/pricing/route.ts
// API route for fetching personalized pricing for a product

import { NextRequest, NextResponse } from 'next/server';
import { getProductBySlug } from '@/lib/actions/product.actions';
import { prisma } from '@/lib/prisma';
import type { ProductWithDetails } from '@/lib/types';

// Extended type with display pricing fields
interface ProductWithDisplayPricing extends Omit<ProductWithDetails, 'offers' | 'variants'> {
  offers: (ProductWithDetails['offers'][0] & {
    display_price?: number;
    display_currency?: string;
    exchange_rate?: number;
  })[];
  variants: (ProductWithDetails['variants'][0] & {
    display_price?: number;
    display_currency?: string;
    exchange_rate?: number;
  })[];
}

interface RouteParams {
  params: Promise<{ productId: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { productId } = await params;
    const { searchParams } = new URL(request.url);
    const currency = searchParams.get('currency') || 'USD';
    const locale = searchParams.get('locale') || 'en';

    // Find product by ID to get slug for fetching
    const productRecord = await prisma.products.findUnique({
      where: {
        id: parseInt(productId),
        can_show: true,
      },
      select: {
        translations: {
          where: { language_code: locale },
          take: 1,
          select: { slug: true },
        },
      },
    });

    if (!productRecord?.translations[0]?.slug) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    const slug = productRecord.translations[0].slug;

    // Fetch product with personalized pricing
    const product = await getProductBySlug(slug, locale, currency);

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    // Cast product to include display pricing fields
    const productWithPricing = product as ProductWithDisplayPricing;

    // Return only the pricing-related data (with display pricing added by getProductBySlug)
    const pricingData = {
      id: productWithPricing.id,
      offers: productWithPricing.offers,
      variants: productWithPricing.variants,
    };

    return NextResponse.json(pricingData);
  } catch (error) {
    console.error('Error in GET /api/products/[productId]/pricing:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}