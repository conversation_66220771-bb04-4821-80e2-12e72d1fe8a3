// app/api/products/batch-pricing/route.ts
// API route for batch pricing calculations

import { NextRequest, NextResponse } from 'next/server';
import { pricingService } from '@/lib/services/pricing';

interface BatchPricingRequest {
  productIds: number[];
  currency: string;
  locale: string;
}

interface BatchPricingResponse {
  [productId: number]: {
    offers: Array<{
      id: string;
      min_quantity: number | null;
      price_low: number;
      price_high: number | null;
      currency: string;
      quantity_info: string | null;
      display_price?: number;
      display_currency?: string;
      exchange_rate?: number;
    }>;
    variants: Array<{
      id: string;
      original_variant_name: string;
      original_variant_type: string;
      price_low: number;
      price_high: number | null;
      currency: string;
      available_quantity: number | null;
      min_quantity: number | null;
      display_price?: number;
      display_currency?: string;
      exchange_rate?: number;
    }>;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: BatchPricingRequest = await request.json();
    const { productIds, currency, locale } = body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { error: 'productIds array is required and cannot be empty' },
        { status: 400 }
      );
    }

    if (!currency || typeof currency !== 'string') {
      return NextResponse.json(
        { error: 'currency is required and must be a string' },
        { status: 400 }
      );
    }

    // Limit batch size to prevent abuse
    if (productIds.length > 100) {
      return NextResponse.json(
        { error: 'Maximum 100 products per batch request' },
        { status: 400 }
      );
    }

    // Fetch product data for pricing calculation
    const products = await fetchProductsForPricing(productIds, locale);

    if (products.length === 0) {
      return NextResponse.json(
        { error: 'No valid products found' },
        { status: 404 }
      );
    }

    // Prepare pricing inputs
    const pricingInputs = products.flatMap(product => [
      // Offers pricing
      ...product.offers.map(offer => ({
        costPriceCNY: Number(offer.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: currency,
        },
      })),
      // Variants pricing
      ...product.variants.map(variant => ({
        costPriceCNY: Number(variant.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: currency,
        },
      })),
    ]);

    // Calculate prices in batch
    const pricingResults = await pricingService.calculatePrices(pricingInputs);

    // Organize results by product
    const result: BatchPricingResponse = {};
    let resultIndex = 0;

    for (const product of products) {
      result[product.id] = {
        offers: product.offers.map((offer, offerIndex) => {
          const pricingResult = pricingResults[resultIndex + offerIndex];
          return {
            id: offer.id.toString(),
            min_quantity: offer.min_quantity ? Number(offer.min_quantity) : null,
            price_low: Number(offer.price_low),
            price_high: offer.price_high ? Number(offer.price_high) : null,
            currency: offer.currency,
            quantity_info: offer.quantity_info,
            ...(pricingResult && {
              display_price: pricingResult.displayPrice,
              display_currency: pricingResult.currency,
              exchange_rate: pricingResult.exchangeRate,
            }),
          };
        }),
        variants: product.variants.map((variant, variantIndex) => {
          const pricingResult = pricingResults[resultIndex + product.offers.length + variantIndex];
          return {
            id: variant.id.toString(),
            original_variant_name: variant.original_variant_name,
            original_variant_type: variant.original_variant_type,
            price_low: Number(variant.price_low),
            price_high: variant.price_high ? Number(variant.price_high) : null,
            currency: variant.currency,
            available_quantity: variant.available_quantity ? Number(variant.available_quantity) : null,
            min_quantity: variant.min_quantity ? Number(variant.min_quantity) : null,
            ...(pricingResult && {
              display_price: pricingResult.displayPrice,
              display_currency: pricingResult.currency,
              exchange_rate: pricingResult.exchangeRate,
            }),
          };
        }),
      };

      resultIndex += product.offers.length + product.variants.length;
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in batch pricing API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function fetchProductsForPricing(productIds: number[], locale: string) {
  const { prisma } = await import('@/lib/prisma');

  return await prisma.products.findMany({
    where: {
      id: { in: productIds },
      can_show: true,
    },
    select: {
      id: true,
      marketplace: true,
      offers: {
        orderBy: { min_quantity: 'asc' },
        take: 20, // Limit offers
      },
      variants: {
        take: 50, // Limit variants
      },
      categories: {
        take: 1,
        select: {
          category: {
            select: { id: true },
          },
        },
      },
    },
  });
}