'use client';

// lib/actions/client/product-client.actions.ts
// Client-side actions for product-related operations

/**
 * Fetch personalized pricing for a product
 */
export async function getPersonalizedProductPricing(productId: number, userCurrency: string, locale: string = 'en') {
  try {
    const response = await fetch(`/api/products/${productId}/pricing?currency=${userCurrency}&locale=${locale}`);
    if (!response.ok) {
      throw new Error('Failed to fetch personalized pricing');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching personalized pricing:', error);
    return null;
  }
}

/**
 * Fetch batch pricing for multiple products
 */
export async function getBatchProductPricing(productIds: number[], userCurrency: string, locale: string = 'en') {
  try {
    const response = await fetch('/api/products/batch-pricing', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        productIds,
        currency: userCurrency,
        locale,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch batch pricing');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching batch pricing:', error);
    return null;
  }
}