'use server';

// lib/actions/cart.actions.ts
// Server actions for Firestore-based cart management

import { doc, getDoc, setDoc, updateDoc, deleteField } from 'firebase/firestore';
import { db } from '@/lib/firebase/client';
import { getCurrentUser } from './auth.actions';
import { pricingService } from '@/lib/services/pricing';
import { exchangeRateService } from '@/lib/services/exchange-rate';
import { CartData, FirestoreCart, FirestoreCartItem } from '@/lib/types';

/**
 * Get user's cart from Firestore
 */
export async function getCart(): Promise<CartData | null> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return null;
    }

    const cartRef = doc(db, 'carts', user.uid);
    const cartSnap = await getDoc(cartRef);

    if (!cartSnap.exists()) {
      // Return empty cart
      return {
        items: [],
        totals: {
          subtotal: 0,
          total: 0,
          currency: 'USD',
        },
        itemCount: 0,
      };
    }

    const cartData = cartSnap.data() as FirestoreCart;

    // Convert to client format
    const clientItems = cartData.items.map(item => ({
      productId: item.productId,
      variantId: item.variantId,
      quantity: item.quantity,
      productName: item.productName,
      productSlug: item.productSlug,
      variantName: item.variantName,
      price: item.price,
      currency: item.currency,
      imageUrl: item.imageUrl,
      marketplace: item.marketplace,
    }));

    return {
      items: clientItems,
      totals: {
        subtotal: cartData.totals.subtotal,
        total: cartData.totals.total,
        currency: cartData.currency,
      },
      itemCount: cartData.itemCount,
    };
  } catch (error) {
    console.error('Error getting cart:', error);
    return null;
  }
}

/**
 * Add item to cart with server-side price calculation
 */
export async function addToCart(data: {
  productId: number;
  variantId?: number;
  quantity: number;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const { productId, variantId, quantity } = data;

    // Get user's currency preference
    let userCurrency = 'USD';
    try {
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (userDoc.exists()) {
        userCurrency = userDoc.data().preferredCurrency || 'USD';
      }
    } catch (error) {
      console.error('Error getting user currency:', error);
    }

    // Fetch product data
    const { prisma } = await import('@/lib/prisma');
    const product = await prisma.products.findUnique({
      where: {
        id: productId,
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        product_url: true,
        translations: {
          where: { language_code: 'en' }, // Default to English
          take: 1,
          select: { name: true, slug: true },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          select: { image_url: true },
        },
        offers: {
          orderBy: { min_quantity: 'asc' },
          select: { min_quantity: true, price_low: true, price_high: true },
        },
        variants: variantId ? {
          where: { id: variantId },
          take: 1,
          select: { price_low: true, price_high: true },
        } : false,
      },
    });

    if (!product || product.offers.length === 0) {
      return { success: false, error: 'Product not found or not available' };
    }

    // Determine cost price based on variant or quantity-based offer
    let costPriceCNY: number;

    if (variantId && product.variants && product.variants.length > 0) {
      // Use variant price if variant selected
      costPriceCNY = Number(product.variants[0].price_low);
    } else {
      // Find appropriate offer based on quantity
      const applicableOffer = product.offers
        .sort((a, b) => Number(a.min_quantity || 0) - Number(b.min_quantity || 0))
        .find(offer => quantity >= Number(offer.min_quantity || 1));

      costPriceCNY = Number(applicableOffer?.price_low || product.offers[0].price_low);
    }

    // Calculate price server-side using pricing service
    const pricingResult = await pricingService.calculatePrice(costPriceCNY, {
      productId,
      marketplace: product.marketplace,
      userCurrency,
    });

    // Prepare cart item
    const cartItem: FirestoreCartItem = {
      productId,
      variantId,
      quantity,
      price: pricingResult.displayPrice,
      currency: pricingResult.currency,
      productName: product.translations[0]?.name || 'Product',
      productSlug: product.translations[0]?.slug || `product-${productId}`,
      variantName: undefined, // Will be populated when variant data is needed
      imageUrl: product.product_images[0]?.image_url,
      marketplace: product.marketplace,
      addedAt: new Date(),
    };

    // Get or create cart
    const cartRef = doc(db, 'carts', user.uid);
    const cartSnap = await getDoc(cartRef);

    let existingItems: FirestoreCartItem[] = [];
    let currentCurrency = userCurrency;

    if (cartSnap.exists()) {
      const cartData = cartSnap.data() as FirestoreCart;
      existingItems = cartData.items;
      currentCurrency = cartData.currency;

      // Check if currency changed - if so, we need to recalculate all prices
      if (currentCurrency !== userCurrency) {
        // Currency changed, recalculate all items
        const recalculatedItems = await Promise.all(
          existingItems.map(async (item) => {
            const itemProduct = await prisma.products.findUnique({
              where: { id: item.productId },
              select: {
                offers: {
                  orderBy: { min_quantity: 'asc' },
                  select: { min_quantity: true, price_low: true },
                },
                variants: item.variantId ? {
                  where: { id: item.variantId },
                  take: 1,
                  select: { price_low: true },
                } : false,
                marketplace: true,
              },
            });

            if (itemProduct) {
              // Determine cost price based on variant or quantity-based offer
              let itemCostPriceCNY: number;

              if (item.variantId && itemProduct.variants && itemProduct.variants.length > 0) {
                // Use variant price if variant was selected
                itemCostPriceCNY = Number(itemProduct.variants[0].price_low);
              } else {
                // Find appropriate offer based on item quantity
                const applicableOffer = itemProduct.offers
                  .sort((a, b) => Number(a.min_quantity || 0) - Number(b.min_quantity || 0))
                  .find(offer => item.quantity >= Number(offer.min_quantity || 1));

                itemCostPriceCNY = Number(applicableOffer?.price_low || itemProduct.offers[0].price_low);
              }

              const itemPricingResult = await pricingService.calculatePrice(
                itemCostPriceCNY,
                {
                  productId: item.productId,
                  marketplace: itemProduct.marketplace,
                  userCurrency,
                }
              );

              return {
                ...item,
                price: itemPricingResult.displayPrice,
                currency: itemPricingResult.currency,
              };
            }
            return item;
          })
        );
        existingItems = recalculatedItems;
        currentCurrency = userCurrency;
      }
    }

    // Check if item already exists
    const existingIndex = existingItems.findIndex(
      item => item.productId === productId && item.variantId === variantId
    );

    if (existingIndex > -1) {
      // Update quantity
      existingItems[existingIndex].quantity += quantity;
    } else {
      // Add new item
      existingItems.push(cartItem);
    }

    // Calculate totals (shipping and taxes will be applied via pricing rules at checkout)
    const subtotal = existingItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal;

    // Save to Firestore
    const cartData: FirestoreCart = {
      userId: user.uid,
      items: existingItems,
      currency: currentCurrency,
      totals: {
        subtotal,
        total,
      },
      itemCount: existingItems.reduce((sum, item) => sum + item.quantity, 0),
      updatedAt: new Date(),
      createdAt: cartSnap.exists() ? (cartSnap.data() as FirestoreCart).createdAt : new Date(),
    };

    await setDoc(cartRef, cartData);

    return { success: true };
  } catch (error) {
    console.error('Error adding to cart:', error);
    return { success: false, error: 'Failed to add item to cart' };
  }
}

/**
 * Update item quantity in cart
 */
export async function updateCartQuantity(data: {
  productId: number;
  quantity: number;
  variantId?: number;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    if (data.quantity <= 0) {
      return await removeFromCart({ productId: data.productId, variantId: data.variantId });
    }

    const cartRef = doc(db, 'carts', user.uid);
    const cartSnap = await getDoc(cartRef);

    if (!cartSnap.exists()) {
      return { success: false, error: 'Cart not found' };
    }

    const cartData = cartSnap.data() as FirestoreCart;
    const itemIndex = cartData.items.findIndex(
      item => item.productId === data.productId && item.variantId === data.variantId
    );

    if (itemIndex === -1) {
      return { success: false, error: 'Item not found in cart' };
    }

    // Update quantity
    cartData.items[itemIndex].quantity = data.quantity;

    // Recalculate totals (shipping and taxes will be applied via pricing rules at checkout)
    const subtotal = cartData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal;

    const updatedCart: FirestoreCart = {
      ...cartData,
      totals: {
        subtotal,
        total,
      },
      itemCount: cartData.items.reduce((sum, item) => sum + item.quantity, 0),
      updatedAt: new Date(),
    };

    await setDoc(cartRef, updatedCart);

    return { success: true };
  } catch (error) {
    console.error('Error updating cart quantity:', error);
    return { success: false, error: 'Failed to update item quantity' };
  }
}

/**
 * Remove item from cart
 */
export async function removeFromCart(data: {
  productId: number;
  variantId?: number;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const cartRef = doc(db, 'carts', user.uid);
    const cartSnap = await getDoc(cartRef);

    if (!cartSnap.exists()) {
      return { success: false, error: 'Cart not found' };
    }

    const cartData = cartSnap.data() as FirestoreCart;

    // Remove item
    cartData.items = cartData.items.filter(
      item => !(item.productId === data.productId && item.variantId === data.variantId)
    );

    if (cartData.items.length === 0) {
      // Delete empty cart
      await setDoc(cartRef, {});
      return { success: true };
    }

    // Recalculate totals (shipping and taxes will be applied via pricing rules at checkout)
    const subtotal = cartData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const total = subtotal;

    const updatedCart: FirestoreCart = {
      ...cartData,
      totals: {
        subtotal,
        total,
      },
      itemCount: cartData.items.reduce((sum, item) => sum + item.quantity, 0),
      updatedAt: new Date(),
    };

    await setDoc(cartRef, updatedCart);

    return { success: true };
  } catch (error) {
    console.error('Error removing from cart:', error);
    return { success: false, error: 'Failed to remove item from cart' };
  }
}

/**
 * Clear entire cart
 */
export async function clearCart(): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    const cartRef = doc(db, 'carts', user.uid);

    // Delete the cart document
    await setDoc(cartRef, {});

    return { success: true };
  } catch (error) {
    console.error('Error clearing cart:', error);
    return { success: false, error: 'Failed to clear cart' };
  }
}

/**
 * Migrate cart from localStorage to Firestore (for existing users)
 */
export async function migrateCart(localCartItems: Array<{
  productId: number;
  variantId?: number;
  quantity: number;
}>): Promise<{ success: boolean; error?: string }> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: 'Authentication required' };
    }

    // Process each item through addToCart
    for (const item of localCartItems) {
      const result = await addToCart(item);
      if (!result.success) {
        console.warn('Failed to migrate cart item:', item, result.error);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error migrating cart:', error);
    return { success: false, error: 'Failed to migrate cart' };
  }
}