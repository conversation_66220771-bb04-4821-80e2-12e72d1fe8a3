'use server';

// lib/actions/order.actions.ts
// Server actions for order management

import { Prisma } from '@/app/generated/prisma';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from './auth.actions';
import { revalidatePath } from 'next/cache';
import type { CartItem, Address } from '@/lib/types';
import { ORDERS_PER_PAGE } from '@/lib/constants';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

/**
 * Create a new order from cart items with server-side price calculation
 */
export async function createOrder(data: {
  items: CartItem[];
  shippingAddress: Address;
  userCurrency: string;
  shippingCost: number;
}) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return {
        success: false,
        error: 'Not authenticated',
      };
    }

    const { items, shippingAddress, userCurrency, shippingCost } = data;

    if (!items.length) {
      return {
        success: false,
        error: 'Cart is empty',
      };
    }

    // Import services for server-side price calculation
    const { pricingService } = await import('@/lib/services/pricing');
    const { exchangeRateService } = await import('@/lib/services/exchange-rate');

    // Get current exchange rate for the user's currency
    const exchangeRate = await exchangeRateService.getExchangeRate('CNY', userCurrency);

    // Fetch product data to get base prices
    const itemProductIds = items.map(item => item.productId);
    const itemProducts = await prisma.products.findMany({
      where: {
        id: { in: itemProductIds },
        can_show: true,
      },
      select: {
        id: true,
        marketplace: true,
        product_url: true,
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
      },
    });

    // Create map of product data
    const productDataMap = new Map(
      itemProducts.map(product => [product.id, {
        costPriceCNY: product.offers[0]?.price_low ? Number(product.offers[0].price_low) : 0,
        marketplace: product.marketplace,
        productUrl: product.product_url,
      }])
    );

    // Calculate final prices for each cart item server-side
    const orderItems = await Promise.all(
      items.map(async (item) => {
        const productData = productDataMap.get(item.productId);
        if (!productData) {
          throw new Error(`Product ${item.productId} not found or not available`);
        }

        const pricingResult = await pricingService.calculatePrice(productData.costPriceCNY, {
          productId: item.productId,
          marketplace: productData.marketplace,
          userCurrency: userCurrency,
        });

        return {
          ...item,
          price: pricingResult.displayPrice,
          currency: pricingResult.currency,
          costPriceCNY: productData.costPriceCNY,
          productUrl: productData.productUrl,
          marketplace: productData.marketplace,
        };
      })
    );

    // Calculate total from server-calculated prices
    const subtotal = orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const totalAmount = subtotal + shippingCost;

    // Create order with order items in a transaction
    const order = await prisma.$transaction(async (tx) => {
      // Create order
      const newOrder = await tx.orders.create({
        data: {
          customer_id: user.customerId,
          shipping_address: shippingAddress as unknown as Prisma.InputJsonValue,
          status: 'pending',
          total_amount: totalAmount,
          currency: userCurrency,
          exchange_rate: exchangeRate,
          shipping_cost: shippingCost,
        },
      });

      // Prepare order items data for batch insert using server-calculated prices
      const orderItemsData = orderItems.map(item => ({
        order_id: newOrder.id,
        product_id: item.productId,
        variant_id: item.variantId || null,
        quantity: item.quantity,
        price_per_unit: item.price,
        marketplace_product_url: item.productUrl,
        marketplace_notes: `Marketplace: ${item.marketplace}`,
      }));

      // Batch create order items
      if (orderItemsData.length > 0) {
        await tx.order_items.createMany({
          data: orderItemsData,
        });
      }

      return newOrder;
    });

    revalidatePath('/account/orders');

    return {
      success: true,
      orderId: order.id,
    };
  } catch (error) {
    console.error('Error creating order:', error);
    return {
      success: false,
      error: 'Failed to create order',
    };
  }
}

/**
 * Get user orders with cursor-based pagination
 */
export async function getUserOrders(cursor?: string, limit: number = ORDERS_PER_PAGE) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const orders = await prisma.orders.findMany({
        where: {
          customer_id: user.customerId,
          ...buildCursorWhereClause(cursor, 'created', 'id'),
        },
        orderBy: buildCursorOrderBy('created', 'id'),
        take: limit + 1, // Fetch one extra to determine hasMore
        include: {
          order_items: {
            take: 5, // Limit items shown in list
            include: {
              product: {
                include: {
                  translations: {
                    take: 1,
                  },
                },
              },
            },
          },
        },
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(orders, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    return {
      orders: paginationResult.data,
      pagination: {
        limit,
        hasMore: paginationResult.hasMore,
        nextCursor: paginationResult.nextCursor,
        prevCursor: paginationResult.prevCursor,
      },
    };
  } catch (error) {
    console.error('Error fetching orders:', error);
    return null;
  }
}

/**
 * Get single order by ID
 * Only returns if order belongs to current user
 */
export async function getOrderById(orderId: string) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return null;
    }

    const order = await prisma.orders.findFirst({
      where: {
        id: orderId,
        customer_id: user.customerId, // Security: only user's own orders
      },
      include: {
        order_items: {
          include: {
            product: {
              include: {
                translations: {
                  take: 1,
                },
                product_images: {
                  where: {
                    image_type: 'preview',
                  },
                  take: 1,
                },
              },
            },
            variant: {
              include: {
                translations: {
                  take: 1,
                },
              },
            },
          },
        },
        payments: true,
      },
    });

    return order;
  } catch (error) {
    console.error('Error fetching order:', error);
    return null;
  }
}
