'use server';

// lib/actions/product.actions.ts
// Optimized server actions for product catalog with proper limits

import { prisma } from '@/lib/prisma';
import { PRODUCTS_PER_PAGE, MAX_PRODUCT_IMAGES_DISPLAY } from '@/lib/constants';
import type { ProductFilters, PaginatedResponse, ProductListItem, ProductWithDetails } from '@/lib/types';
import { Prisma, Marketplace } from '@/app/generated/prisma';
import { pricingService } from '@/lib/services/pricing';
import {
  buildCursorWhereClause,
  buildCursorOrderBy,
  buildCustomOrderByWithCursor,
  applyCursorPagination
} from '@/lib/utils/cursor-pagination';

/**
 * Convert Prisma Decimal objects to numbers for client serialization
 * Creates plain JSON-serializable objects
 */
function serializeForClient<T>(obj: T): T {
  if (obj === null || obj === undefined) return obj;

  if (typeof obj === 'object') {
    // Convert Decimal to number
    if (obj instanceof Prisma.Decimal) {
      return Number(obj) as T;
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(serializeForClient) as T;
    }

    // Skip functions and non-plain objects
    if (typeof obj === 'function' || obj.constructor !== Object) {
      return undefined as T;
    }

    // Create plain object with only serializable properties
    const result = {} as Record<string, unknown>;
    for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
      // Only include primitive values and plain objects/arrays
      if (
        typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean' ||
        value === null ||
        value === undefined ||
        (typeof value === 'object' && (
          value instanceof Prisma.Decimal ||
          Array.isArray(value) ||
          value.constructor === Object
        ))
      ) {
        result[key] = serializeForClient(value);
      }
    }
    return result as T;
  }

  return obj;
}

/**
 * Get products with filters, search, and pagination
 * Optimized for large datasets (200k+ products)
 */
export async function getProducts(
  filters: ProductFilters = {},
  locale: string = 'en',
  _userId?: string,
  _request?: Request,
  userCurrency?: string
): Promise<PaginatedResponse<ProductListItem>> {
  try {
    // Determine user's currency (passed from server component or default)
    const finalUserCurrency = userCurrency || 'USD';

    const {
      search,
      categoryId,
      marketplace,
      minPrice,
      maxPrice,
      sortBy = 'newest',
      cursor,
      limit = PRODUCTS_PER_PAGE,
    } = filters;

    // Build where clause
    const where: Prisma.productsWhereInput = {
      can_show: true, // Only show visible products
      ...buildCursorWhereClause(cursor, 'created', 'id'),
    };

    // Search filter (search in translations)
    if (search) {
      where.translations = {
        some: {
          language_code: locale,
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { slug: { contains: search, mode: 'insensitive' } },
          ],
        },
      };
    }

    // Category filter (using explicit join table)
    if (categoryId) {
      where.categories = {
        some: {
          category_id: categoryId,
        },
      };
    }

    // Marketplace filter
    if (marketplace) {
      where.marketplace = marketplace as Marketplace;
    }

    // Price filter (using offers table)
    if (minPrice !== undefined || maxPrice !== undefined) {
      where.offers = {
        some: {
          AND: [
            minPrice !== undefined ? { price_low: { gte: minPrice } } : {},
            maxPrice !== undefined ? { price_low: { lte: maxPrice } } : {},
          ],
        },
      };
    }

    // Build orderBy clause for cursor pagination
    // For price sorting, we'll sort in-memory after calculating actual prices
    // to avoid expensive database operations on 200k+ products
    let orderBy: Prisma.productsOrderByWithRelationInput[];
    const isPriceSorting = sortBy === 'price_asc' || sortBy === 'price_desc';

    if (isPriceSorting) {
      // For price sorting, use default ordering and sort in-memory later
      orderBy = buildCursorOrderBy('created', 'id');
    } else {
      switch (sortBy) {
        case 'popular':
          orderBy = buildCustomOrderByWithCursor({ user_activity: { _count: 'desc' } }, 'created', 'id');
          break;
        case 'newest':
        default:
          // For newest (default), use standard cursor ordering
          orderBy = buildCursorOrderBy('created', 'id');
          break;
      }
    }

    // Execute query with cursor pagination
    // Fetch limit + 1 to check if there are more items
    const products = await prisma.products.findMany({
        where,
        orderBy,
        take: limit + 1, // Fetch one extra to determine hasMore
        select: {
          id: true,
          original_name: true,
          marketplace: true,
          translations: {
            where: { language_code: locale },
            take: 1,
            select: {
              name: true,
              slug: true,
              language_code: true,
            },
          },
          product_images: {
            where: { image_type: 'preview' },
            take: 1, // Only get first preview image for listing
            orderBy: { id: 'asc' },
            select: {
              image_url: true,
              image_type: true,
            },
          },
          offers: {
            orderBy: { price_low: 'asc' },
            take: 1, // Only get lowest price offer
            select: {
              price_low: true,
              price_high: true,
              currency: true,
              min_quantity: true,
            },
          },
          categories: {
            take: 3, // Limit categories shown
            select: {
              category: {
                select: {
                  id: true,
                  translations: {
                    where: { language_code: locale },
                    take: 1,
                    select: {
                      name: true,
                      slug: true,
                    },
                  },
                },
              },
            },
          },
          // Include created and id for cursor pagination
          created: true,
        },
      });

    // Apply cursor pagination logic
    const paginationResult = applyCursorPagination(products, {
      cursor,
      limit,
      timestampField: 'created',
      idField: 'id',
    });

    // Batch calculate prices for all products to optimize performance
    const pricingInputs = paginationResult.data
      .filter(product => product.offers[0]) // Only products with offers
      .map(product => ({
        costPriceCNY: Number(product.offers[0].price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: finalUserCurrency,
        },
      }));

    const pricingResults = await pricingService.calculatePrices(pricingInputs);

    // Create a lookup map for O(1) access
    const pricingMap = new Map(
      pricingResults.map(result => [result.productId, result])
    );

    // Apply pricing results to products
    const productsWithPrices = paginationResult.data.map(product => {
      const offer = product.offers[0];
      if (!offer) return product;

      const pricingResult = pricingMap.get(product.id);
      if (!pricingResult) return product;

      // Update offer with calculated price
      return {
        ...product,
        offers: [{
          ...offer,
          display_price: pricingResult.displayPrice,
          display_currency: pricingResult.currency,
          exchange_rate: pricingResult.exchangeRate,
        }],
      };
    });

    // Apply in-memory price sorting if needed
    let finalProducts = productsWithPrices;
    if (isPriceSorting) {
      finalProducts = [...productsWithPrices].sort((a, b) => {
        const offerA = a.offers[0];
        const offerB = b.offers[0];

        // Get display price if available, otherwise use base price
        const priceA = ('display_price' in offerA) ? offerA.display_price : Number(offerA?.price_low) || 0;
        const priceB = ('display_price' in offerB) ? offerB.display_price : Number(offerB?.price_low) || 0;

        if (sortBy === 'price_asc') {
          return priceA - priceB;
        } else {
          return priceB - priceA;
        }
      });
    }

    return serializeForClient({
      data: finalProducts as ProductListItem[],
      pagination: {
        limit,
        hasMore: paginationResult.hasMore,
        nextCursor: paginationResult.nextCursor,
        prevCursor: paginationResult.prevCursor,
      },
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      data: [],
      pagination: {
        limit: PRODUCTS_PER_PAGE,
        hasMore: false,
        nextCursor: undefined,
        prevCursor: undefined,
      },
    };
  }
}

/**
 * Get single product by slug with all details
 * Optimized to limit related data, includes personalized pricing
 */
export async function getProductBySlug(slug: string, locale: string = 'en', userCurrency?: string): Promise<ProductWithDetails | null> {
  try {
    // Determine user's currency (passed or default)
    const finalUserCurrency = userCurrency || "USD";

    // First find the product by slug in translations
    const translation = await prisma.product_translations.findFirst({
      where: {
        slug,
        language_code: locale,
      },
      select: {
        product_id: true,
      },
    });

    if (!translation) {
      return null;
    }

    // Fetch full product details with limits
    const product = await prisma.products.findUnique({
      where: {
        id: translation.product_id,
        can_show: true,
      },
      include: {
        translations: {
          where: { language_code: locale },
        },
        variants: {
          take: 50, // Limit variants to 50
          include: {
            translations: {
              where: { language_code: locale },
            },
          },
        },
        offers: {
          orderBy: { min_quantity: 'asc' },
          take: 20, // Limit offers to 20
        },
        product_images: {
          orderBy: { id: 'asc' },
        },
        product_attributes: {
          take: 30, // Limit attributes to 30
          include: {
            translations: {
              where: { language_code: locale },
            },
          },
        },
        categories: {
          take: 5, // Limit categories to 5
          include: {
            category: {
              include: {
                translations: {
                  where: { language_code: locale },
                },
              },
            },
          },
        },
      },
    });

    if (!product) {
      return null;
    }

    // Calculate personalized pricing for product offers
    if (product.offers.length > 0) {
      const pricingInputs = product.offers.map(offer => ({
        costPriceCNY: Number(offer.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: finalUserCurrency,
        },
      }));

      const pricingResults = await pricingService.calculatePrices(pricingInputs);

      // Apply pricing results to offers
      product.offers = product.offers.map((offer, index) => {
        const pricingResult = pricingResults[index];
        if (pricingResult) {
          return {
            ...offer,
            display_price: pricingResult.displayPrice,
            display_currency: pricingResult.currency,
            exchange_rate: pricingResult.exchangeRate,
          };
        }
        return offer;
      });
    }

    // Calculate pricing for variants if they exist
    if (product.variants.length > 0) {
      const variantPricingInputs = product.variants.map(variant => ({
        costPriceCNY: Number(variant.price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: finalUserCurrency,
        },
      }));

      const variantPricingResults = await pricingService.calculatePrices(variantPricingInputs);

      // Apply pricing results to variants
      product.variants = product.variants.map((variant, index) => {
        const pricingResult = variantPricingResults[index];
        if (pricingResult) {
          return {
            ...variant,
            display_price: pricingResult.displayPrice,
            display_currency: pricingResult.currency,
            exchange_rate: pricingResult.exchangeRate,
          };
        }
        return variant;
      });
    }

    return serializeForClient(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

/**
 * Get featured products for homepage
 * Prioritizes featured products, then fills with most recent visible products with personalized pricing
 */
export async function getFeaturedProducts(locale: string = 'en', limit: number = 12, userCurrency?: string) {
  try {
    // Determine user's currency (passed or default)
    const finalUserCurrency = userCurrency || "USD";

    // First get all featured products
    const featuredProducts = await prisma.products.findMany({
      where: {
        can_show: true,
        featured: true,
      },
      orderBy: {
        created: 'desc',
      },
      take: limit,
      select: {
        id: true,
        original_name: true,
        marketplace: true,
        translations: {
          where: { language_code: locale },
          take: 1,
          select: {
            name: true,
            slug: true,
            language_code: true,
          },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          orderBy: { id: 'asc' },
          select: {
            image_url: true,
            image_type: true,
          },
        },
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 3,
          select: {
            category: {
              select: {
                id: true,
                translations: {
                  where: { language_code: locale },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    let products = featuredProducts;

    // If we need more products to reach the limit, get recent non-featured products
    if (featuredProducts.length < limit) {
      const remainingLimit = limit - featuredProducts.length;
      const featuredProductIds = featuredProducts.map(p => p.id);

      const recentProducts = await prisma.products.findMany({
        where: {
          can_show: true,
          featured: false,
          id: {
            notIn: featuredProductIds, // Exclude already featured products
          },
        },
        orderBy: {
          created: 'desc',
        },
        take: remainingLimit,
        select: {
          id: true,
          original_name: true,
          marketplace: true,
          translations: {
            where: { language_code: locale },
            take: 1,
            select: {
              name: true,
              slug: true,
              language_code: true,
            },
          },
          product_images: {
            where: { image_type: 'preview' },
            take: 1,
            orderBy: { id: 'asc' },
            select: {
              image_url: true,
              image_type: true,
            },
          },
          offers: {
            orderBy: { price_low: 'asc' },
            take: 1,
            select: {
              price_low: true,
              price_high: true,
              currency: true,
              min_quantity: true,
            },
          },
          categories: {
            take: 3,
            select: {
              category: {
                select: {
                  id: true,
                  translations: {
                    where: { language_code: locale },
                    take: 1,
                    select: {
                      name: true,
                      slug: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      // Combine featured and recent products
      products = [...featuredProducts, ...recentProducts];
    }

    // Apply pricing to all products
    const pricingInputs = products
      .filter(product => product.offers[0]) // Only products with offers
      .map(product => ({
        costPriceCNY: Number(product.offers[0].price_low),
        context: {
          productId: product.id,
          categoryId: product.categories[0]?.category.id,
          marketplace: product.marketplace as string,
          userCurrency: finalUserCurrency,
        },
      }));

    const pricingResults = await pricingService.calculatePrices(pricingInputs);

    // Create a lookup map for O(1) access
    const pricingMap = new Map(
      pricingResults.map(result => [result.productId, result])
    );

    // Apply pricing results to products
    const productsWithPrices = products.map(product => {
      const offer = product.offers[0];
      if (!offer) return product;

      const pricingResult = pricingMap.get(product.id);
      if (!pricingResult) return product;

      // Update offer with calculated price
      return {
        ...product,
        offers: [{
          ...offer,
          display_price: pricingResult.displayPrice,
          display_currency: pricingResult.currency,
          exchange_rate: pricingResult.exchangeRate,
        }],
      };
    });

    return serializeForClient(productsWithPrices);
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

/**
 * Get related products based on category
 * Limited and optimized query with personalized pricing
 */
export async function getRelatedProducts(
  productId: number,
  locale: string = 'en',
  limit: number = 6,
  userCurrency?: string
) {
  try {
    // Determine user's currency (passed or default)
    const finalUserCurrency = userCurrency || "USD";
    // Get product categories (using explicit join table)
    const product = await prisma.products.findUnique({
      where: { id: productId },
      select: {
        categories: {
          take: 1,
          select: { category_id: true },
        },
      },
    });

    if (!product || !product.categories.length) {
      return [];
    }

    const categoryId = product.categories[0].category_id;

    // Find related products in same category (using explicit join table)
    const relatedProducts = await prisma.products.findMany({
      where: {
        can_show: true,
        id: { not: productId },
        categories: {
          some: {
            category_id: categoryId,
          },
        },
      },
      take: limit,
      select: {
        id: true,
        original_name: true,
        marketplace: true,
        translations: {
          where: { language_code: locale },
          take: 1,
          select: {
            name: true,
            slug: true,
            language_code: true,
          },
        },
        product_images: {
          where: { image_type: 'preview' },
          take: 1,
          orderBy: { id: 'asc' },
          select: {
            image_url: true,
            image_type: true,
          },
        },
        offers: {
          orderBy: { price_low: 'asc' },
          take: 1,
          select: {
            price_low: true,
            price_high: true,
            currency: true,
            min_quantity: true,
          },
        },
        categories: {
          take: 3,
          select: {
            category: {
              select: {
                id: true,
                translations: {
                  where: { language_code: locale },
                  take: 1,
                  select: {
                    name: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Calculate personalized pricing for related products
    let finalRelatedProducts = relatedProducts;
    if (relatedProducts.length > 0) {
      const pricingInputs = relatedProducts
        .filter(product => product.offers[0]) // Only products with offers
        .map(product => ({
          costPriceCNY: Number(product.offers[0].price_low),
          context: {
            productId: product.id,
            categoryId: product.categories[0]?.category.id,
            marketplace: product.marketplace as string,
            userCurrency: finalUserCurrency,
          },
        }));

      const pricingResults = await pricingService.calculatePrices(pricingInputs);

      // Create a lookup map for O(1) access
      const pricingMap = new Map(
        pricingResults.map(result => [result.productId, result])
      );

      // Apply pricing results to products
      finalRelatedProducts = relatedProducts.map(product => {
        const offer = product.offers[0];
        if (!offer) return product;

        const pricingResult = pricingMap.get(product.id);
        if (!pricingResult) return product;

        // Update offer with calculated price
        return {
          ...product,
          offers: [{
            ...offer,
            display_price: pricingResult.displayPrice,
            display_currency: pricingResult.currency,
            exchange_rate: pricingResult.exchangeRate,
          }],
        };
      });
    }

    return serializeForClient(finalRelatedProducts);

    return relatedProducts;
  } catch (error) {
    console.error('Error fetching related products:', error);
    return [];
  }
}

/**
 * Category with translations type
 */
type CategoryWithTranslations = {
  id: number;
  parent_id: number | null;
  created: Date;
  updated: Date;
  translations: {
    id: bigint;
    category_id: number;
    language_code: string;
    name: string;
    slug: string;
  }[];
  _count: {
    products: number;
  };
};

/**
 * Get categories with product count
 * Optimized for navigation
 */
export async function getCategories(locale: string = 'en'): Promise<CategoryWithTranslations[]> {
  try {
    // Using select for optimal performance - only fetch needed fields
    const categories = await prisma.categories.findMany({
      where: {
        parent_id: null, // Only top-level categories
        products: {
          some: {
            product: {
              can_show: true,
            },
          },
        },
      },
      take: 20, // Limit to 20 main categories
      select: {
        id: true,
        parent_id: true,
        created: true,
        updated: true,
        translations: {
          where: { language_code: locale },
          select: {
            id: true,
            category_id: true,
            language_code: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: {
              where: {
                product: {
                  can_show: true,
                },
              },
            },
          },
        },
      },
      orderBy: {
        id: 'asc',
      },
    });

    return serializeForClient(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}
