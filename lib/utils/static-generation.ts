// lib/utils/static-generation.ts
// Utilities for static generation of pages

import { prisma } from '@/lib/prisma';

/**
 * Get all product slugs for static generation
 * Used in generateStaticParams to pre-generate all product detail pages
 */
export async function getAllProductSlugsForStaticGeneration(locale: string): Promise<string[]> {
  try {
    const translations = await prisma.product_translations.findMany({
      where: {
        language_code: locale,
        product: {
          can_show: true, // Only include visible products
        },
      },
      select: {
        slug: true,
      },
      orderBy: {
        product_id: 'asc',
      },
    });

    return translations.map(t => t.slug);
  } catch (error) {
    console.error('Error fetching product slugs for static generation:', error);
    return [];
  }
}