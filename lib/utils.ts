// lib/utils.ts
// Utility functions used throughout the application

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Merge Tailwind CSS classes with proper precedence
 * Used extensively with shadcn/ui components
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format currency with proper locale and symbol
 * @param amount - The amount to format
 * @param currency - Currency code (USD, EUR, CNY, etc.) or symbol ($, €, ¥, etc.)
 * @param locale - Locale for formatting (default: en-US)
 */
export function formatCurrency(
  amount: number | string,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Map common currency symbols to their ISO codes
  const currencyMap: Record<string, string> = {
    // Currency switcher symbols
    'FCFA': 'XAF',
    'CFA': 'XOF',
    'R': 'ZAR',
    '₦': 'NGN',
    'KSh': 'KES',
    '₵': 'GHS',
    'E£': 'EGP',
    'MAD': 'MAD',
    'TND': 'TND',
    'DZD': 'DZD',
    '$': 'USD',
    '€': 'EUR',
    '£': 'GBP',
    '¥': 'JPY', // Used for both JPY and CNY
    'C$': 'CAD',
    'A$': 'AUD',
    // Additional common symbols
    '₨': 'INR',
    '₩': 'KRW',
    '₫': 'VND',
    '₪': 'ILS',
    '₱': 'PHP',
    '₺': 'TRY',
    '₴': 'UAH',
    '₸': 'KZT',
    '₼': 'AZN',
    '₾': 'GEL',
    '₽': 'RUB',
    '元': 'CNY', // Chinese Yuan symbol
    '₿': 'BTC', // Bitcoin
  };

  // If currency is a symbol, map it to code
  const currencyCode = currencyMap[currency] || currency.toUpperCase();

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numAmount);
}

/**
 * Format date with proper locale
 */
export function formatDate(
  date: Date | string,
  locale: string = 'en-US',
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  }).format(dateObj);
}

/**
 * Truncate text with ellipsis
 */
export function truncate(text: string, length: number): string {
  if (text.length <= length) return text;
  return text.slice(0, length) + '...';
}

/**
 * Generate slug from text
 */
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Calculate percentage
 */
export function calculatePercentage(value: number, total: number): number {
  if (total === 0) return 0;
  return (value / total) * 100;
}

/**
 * Debounce function for search inputs
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
