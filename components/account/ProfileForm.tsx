'use client';

// components/account/ProfileForm.tsx
// Profile editing form

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { updateProfile } from '@/lib/actions/user.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { CURRENCIES } from '@/lib/constants';
import type { UserProfile } from '@/lib/types';

interface ProfileFormProps {
  user: UserProfile;
  locale: string;
}

export function ProfileForm({ user, locale }: ProfileFormProps) {
  const t = useTranslations('account');
  const router = useRouter();
  const { setCurrency } = useCurrency();

  const [formData, setFormData] = useState({
    fullName: user.fullName || '',
    phone: user.phone || '',
    preferredCurrency: user.preferredCurrency || 'USD',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    try {
      const result = await updateProfile(formData);

      if (result.success) {
        // Update global currency state if currency was changed
        if (formData.preferredCurrency !== user.preferredCurrency) {
          setCurrency(formData.preferredCurrency);
        }

        setMessage({ type: 'success', text: t('profileUpdated') });
        router.refresh();
      } else {
        setMessage({ type: 'error', text: result.error || t('updateError') });
      }
    } catch (error) {
      setMessage({ type: 'error', text: t('updateError') });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {message && (
        <div
          className={`p-3 text-sm rounded-md ${
            message.type === 'success'
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-destructive/10 text-destructive'
          }`}
        >
          {message.text}
        </div>
      )}

      <div className="space-y-2">
        <Label htmlFor="email">{t('email')}</Label>
        <Input
          id="email"
          type="email"
          value={user.email}
          disabled
          className="bg-muted"
        />
        <p className="text-xs text-muted-foreground">{t('emailCannotChange')}</p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="fullName">{t('fullName')}</Label>
        <Input
          id="fullName"
          name="fullName"
          type="text"
          value={formData.fullName}
          onChange={handleChange}
          required
          disabled={isLoading}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">{t('phone')}</Label>
        <Input
          id="phone"
          name="phone"
          type="tel"
          value={formData.phone}
          onChange={handleChange}
          placeholder="+1234567890"
          disabled={isLoading}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="preferredCurrency">{t('preferredCurrency')}</Label>
        <select
          id="preferredCurrency"
          name="preferredCurrency"
          value={formData.preferredCurrency}
          onChange={handleChange}
          disabled={isLoading}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
        >
          {Object.entries(CURRENCIES).map(([code, info]) => (
            <option key={code} value={code}>
              {info.symbol} {info.name} ({code})
            </option>
          ))}
        </select>
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? t('saving') : t('saveChanges')}
      </Button>
    </form>
  );
}

