'use client';

// components/cart/CartClient.tsx
// Shopping cart page client component

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';
import { Trash2, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useCartStore } from '@/hooks/use-cart-store';
import { formatCurrency } from '@/lib/utils';
import { DEFAULT_CURRENCY } from '@/lib/constants';

interface CartClientProps {
  locale: string;
}

export function CartClient({ locale }: CartClientProps) {
    const t = useTranslations('cart');
    const tCommon = useTranslations('common');

    const { data, isLoading, error, initialize, removeItem, updateQuantity } = useCartStore();
    const items = data?.items || [];
    const totals = data?.totals;

   const [mounted, setMounted] = useState(false);

   useEffect(() => {
     setMounted(true);
     // Initialize cart on component mount
     initialize();
   }, [initialize]);

  if (!mounted || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-muted-foreground">{tCommon('loading')}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <p className="text-destructive">Error loading cart: {error}</p>
        <Button onClick={() => initialize()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  if (items.length === 0) {
     return (
       <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
         <ShoppingBag className="h-16 w-16 text-muted-foreground" />
         <h2 className="text-2xl font-semibold">{t('empty')}</h2>
         <p className="text-muted-foreground text-center max-w-md">
           {t('emptyDescription')}
         </p>
         <Link href={`/${locale}/products`}>
           <Button>{t('continueShopping')}</Button>
         </Link>
       </div>
     );
   }

   // If no cart data but somehow got here, show auth required
   if (!data) {
     return (
       <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
         <ShoppingBag className="h-16 w-16 text-muted-foreground" />
         <h2 className="text-2xl font-semibold">{t('signInRequired')}</h2>
         <p className="text-muted-foreground text-center max-w-md">
           {t('signInRequiredDescription')}
         </p>
         <div className="flex gap-2">
           <Link href={`/${locale}/login`}>
             <Button>{t('signIn')}</Button>
           </Link>
           <Link href={`/${locale}/register`}>
             <Button variant="outline">{t('signUp')}</Button>
           </Link>
         </div>
       </div>
     );
   }

  // Use server-calculated totals (shipping and taxes will be calculated at checkout via pricing rules)
  const subtotal = totals?.subtotal || 0;
  const currency = totals?.currency || DEFAULT_CURRENCY;

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
        <p className="text-muted-foreground">
          {t('itemsInCart', { count: items.length })}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {items.map((item) => (
            <Card key={`${item.productId}-${item.variantId || 'no-variant'}`}>
              <CardContent className="p-4">
                <div className="flex gap-4">
                  {/* Image */}
                  <div className="relative w-24 h-24 flex-shrink-0 rounded-md overflow-hidden bg-muted">
                    {item.imageUrl ? (
                      <ImageWithFallback
                        src={item.imageUrl}
                        alt={item.productName}
                        className="object-cover"
                        sizes="96px"
                        fallbackText="Image unavailable"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center text-xs text-muted-foreground">
                        {t('noImage')}
                      </div>
                    )}
                  </div>

                  {/* Details */}
                  <div className="flex-1 space-y-2">
                    <div>
                      <Link
                        href={`/${locale}/products/${item.productSlug}`}
                        className="font-semibold hover:text-primary transition-colors"
                      >
                        {item.productName}
                      </Link>
                      {item.variantName && (
                        <p className="text-sm text-muted-foreground">
                          {item.variantName}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      {/* Quantity */}
                      <div className="flex items-center gap-2">
                        <label className="text-sm text-muted-foreground">
                          {t('quantity')}:
                        </label>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) =>
                            updateQuantity(
                              item.productId,
                              parseInt(e.target.value) || 1,
                              item.variantId
                            )
                          }
                          className="w-20"
                        />
                      </div>

                      {/* Price - Estimate (final pricing at checkout) */}
                      <div className="text-right">
                        <p className="font-semibold">
                          {formatCurrency(item.price * item.quantity, item.currency)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatCurrency(item.price, item.currency)} {t('each')}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {t('finalPricingNote')}
                        </p>
                      </div>
                    </div>

                    {/* Remove Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(item.productId, item.variantId)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      {t('remove')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <Card className="sticky top-20">
            <CardHeader>
              <CardTitle>{t('subtotal')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('subtotal')}</span>
                  <span className="font-medium">
                    {formatCurrency(subtotal, currency)}
                  </span>
                </div>
              </div>

              <div className="bg-muted/50 p-3 rounded-md">
                <p className="text-xs text-muted-foreground">
                  {t('shippingNote')}
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Link href={`/${locale}/checkout/shipping`} className="w-full">
                <Button size="lg" className="w-full">
                  {t('proceedToCheckout')}
                </Button>
              </Link>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}

