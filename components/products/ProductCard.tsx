'use client';

// components/products/ProductCard.tsx
// Product card for listing pages - now uses PricingProvider for personalized pricing

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils';
import type { ProductListItem } from '@/lib/types';
import { MARKETPLACES } from '@/lib/constants';
import { ProductMediaDisplayClient } from './ProductMediaDisplayClient';
import { usePricing } from '@/components/providers/PricingProvider';

interface OfferWithDisplay {
  price_low: number;
  price_high: number | null;
  currency: string;
  min_quantity: number;
  display_price?: number;
  display_currency?: string;
  exchange_rate?: number;
}

interface ProductCardProps {
  product: ProductListItem;
  locale: string;
}

export function ProductCard({ product, locale }: ProductCardProps) {
  const t = useTranslations('products');
  const { getPricing } = usePricing();

  // Get translated name
  const translation = product.translations[0];
  const productName = translation?.name || product.original_name || 'Product';
  const productSlug = translation?.slug || '';

  // Get preview image first, then any image
  const previewImage = product.product_images.find(img => img.image_type === 'preview') ||
                        product.product_images[0];
  const imageUrl = previewImage?.image_url;

  // Get personalized pricing data from PricingProvider
  const pricingData = getPricing(product.id);
  let displayPrice = 0;
  let displayCurrency = 'CNY';
  let lowestOffer = null;

  if (pricingData && pricingData.offers.length > 0) {
    // Use personalized pricing from batch API
    lowestOffer = pricingData.offers[0];
    displayPrice = lowestOffer.display_price || lowestOffer.price_low;
    displayCurrency = lowestOffer.display_currency || lowestOffer.currency;
  } else {
    // Fallback to server-calculated display price
    lowestOffer = product.offers[0] as unknown as OfferWithDisplay;
    displayPrice = lowestOffer ? Number(lowestOffer.display_price || lowestOffer.price_low) : 0;
    displayCurrency = lowestOffer?.display_currency || lowestOffer?.currency || 'CNY';
  }

  // Get first category
  const category = product.categories[0];
  const categoryName = category?.category?.translations?.[0]?.name;

  return (
    <Link href={`/${locale}/products/${productSlug}`}>
      <Card className="group overflow-hidden transition-all hover:shadow-lg">
        {/* Image */}
        <div className={`relative ${previewImage?.image_type === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden bg-muted`}>
          {imageUrl ? (
            <ProductMediaDisplayClient
              src={imageUrl}
              alt={productName}
              type={previewImage?.image_type || 'preview'}
              className="object-cover transition-transform group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="flex h-full items-center justify-center text-muted-foreground">
              {t('noImage')}
            </div>
          )}

          {/* Marketplace Badge */}
          <div className="absolute top-2 right-2">
            <Badge variant="secondary" className="text-xs">
              {MARKETPLACES[product.marketplace]?.icon}{' '}
              {MARKETPLACES[product.marketplace]?.name}
            </Badge>
          </div>
        </div>

        <CardContent className="p-4">
          {/* Category */}
          {categoryName && (
            <p className="text-xs text-muted-foreground mb-1">{categoryName}</p>
          )}

          {/* Product Name */}
          <h3 className="font-semibold line-clamp-2 mb-2 group-hover:text-primary transition-colors">
            {productName}
          </h3>

          {/* Price */}
          <div className="flex items-baseline gap-2">
            <span className="text-lg font-bold text-primary">
              {formatCurrency(displayPrice, displayCurrency)}
            </span>
            {lowestOffer && (
              <span className="text-xs text-muted-foreground">
                {t('from')}
              </span>
            )}
          </div>

          {/* Min Order */}
          {lowestOffer && lowestOffer.min_quantity && Number(lowestOffer.min_quantity) > 1 && (
            <p className="text-xs text-muted-foreground mt-1">
              {t('minOrder')}: {lowestOffer.min_quantity.toString()} {t('pieces')}
            </p>
          )}
        </CardContent>
      </Card>
    </Link>
  );
}

