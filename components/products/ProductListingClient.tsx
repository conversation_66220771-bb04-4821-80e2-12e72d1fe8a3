'use client';

// components/products/ProductListingClient.tsx
// Client component for product listing with filters

import { useState, useTransition, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ProductCard } from './ProductCard';
import { ProductFilters } from './ProductFilters';
import { PricingProvider, type PricingData } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { getBatchProductPricing } from '@/lib/actions/client/product-client.actions';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductListItem, PaginationInfo, CategoryWithTranslations, ProductFiltersState } from '@/lib/types';

interface ProductListingClientProps {
  initialProducts: ProductListItem[];
  initialPagination: PaginationInfo;
  categories: CategoryWithTranslations[];
  locale: string;
  initialFilters: ProductFiltersState;
}

export function ProductListingClient({
  initialProducts,
  initialPagination,
  categories,
  locale,
  initialFilters,
}: ProductListingClientProps) {
  const t = useTranslations('products');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const { currency: userCurrency } = useCurrency();

  const [searchQuery, setSearchQuery] = useState(initialFilters.search || '');
  const [personalizedPricing, setPersonalizedPricing] = useState<Record<number, PricingData> | null>(null);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);

  // Fetch batch pricing when component mounts or currency changes
  useEffect(() => {
    const fetchBatchPricing = async () => {
      if (!userCurrency || userCurrency === DEFAULT_CURRENCY) return;

      setIsLoadingPricing(true);
      try {
        const productIds = initialProducts.map(p => p.id);
        const pricing = await getBatchProductPricing(productIds, userCurrency);
        setPersonalizedPricing(pricing);
      } catch (error) {
        console.error('Failed to fetch batch pricing:', error);
      } finally {
        setIsLoadingPricing(false);
      }
    };

    fetchBatchPricing();
  }, [userCurrency, initialProducts]); // Dependency on products to refetch if list changes

  const updateFilters = (updates: Record<string, string | number | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(updates).forEach(([key, value]) => {
      if (value === undefined || value === null || value === '') {
        params.delete(key);
      } else {
        params.set(key, value.toString());
      }
    });

    // Reset to page 1 when filters change
    if (!updates.page) {
      params.delete('page');
    }

    startTransition(() => {
      router.push(`/${locale}/products?${params.toString()}`);
    });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilters({ search: searchQuery });
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateFilters({ sortBy: e.target.value });
  };

  const handlePageChange = (page: number) => {
    updateFilters({ page });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <PricingProvider pricing={personalizedPricing} isLoading={isLoadingPricing}>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
      {/* Filters Sidebar */}
      <aside className="lg:col-span-1">
        <div className="sticky top-20">
          <ProductFilters
            categories={categories}
            selectedCategory={initialFilters.categoryId}
            selectedMarketplace={initialFilters.marketplace}
            selectedPriceRange={
              initialFilters.minPrice !== undefined || initialFilters.maxPrice !== undefined
                ? { min: initialFilters.minPrice ?? 0, max: initialFilters.maxPrice ?? null }
                : undefined
            }
            onCategoryChange={(categoryId) =>
              updateFilters({ category: categoryId })
            }
            onMarketplaceChange={(marketplace) =>
              updateFilters({ marketplace })
            }
            onPriceRangeChange={(range) =>
              updateFilters({
                minPrice: range?.min ?? undefined,
                maxPrice: range?.max ?? undefined,
              })
            }
            onClearFilters={() => {
              setSearchQuery('');
              router.push(`/${locale}/products`);
            }}
          />
        </div>
      </aside>

      {/* Main Content */}
      <div className="lg:col-span-3 space-y-6">
        {/* Search and Sort */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder={t('searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </form>

          {/* Sort */}
          <select
            value={initialFilters.sortBy}
            onChange={handleSortChange}
            className="flex h-10 w-full sm:w-48 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <option value="newest">{t('sortNewest')}</option>
            <option value="price_asc">{t('sortPriceLow')}</option>
            <option value="price_desc">{t('sortPriceHigh')}</option>
            <option value="popular">{t('sortPopular')}</option>
          </select>
        </div>

        {/* Products Grid */}
        {isPending ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{t('loading')}</p>
          </div>
        ) : initialProducts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{t('noResults')}</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {initialProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  locale={locale}
                />
              ))}
            </div>

            {/* Pagination */}
            {initialPagination.hasMore && (
              <div className="flex items-center justify-center gap-2 mt-8">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(1)}
                  disabled={!initialPagination.prevCursor || isPending}
                >
                  {t('previous')}
                </Button>

                <span className="text-sm text-muted-foreground px-4">
                  {t('showingResults', { count: initialProducts.length })}
                </span>

                <Button
                  variant="outline"
                  onClick={() => handlePageChange(2)}
                  disabled={!initialPagination.hasMore || isPending}
                >
                  {t('next')}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
    </PricingProvider>
  );
}

