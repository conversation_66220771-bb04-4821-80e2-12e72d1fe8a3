// components/products/OrderConstructionForm.tsx
'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Truck, Shield, Package } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import type { ProductWithDetails } from '@/lib/types';
import { useProductPricing, OfferWithDisplay, VariantWithDisplay } from '@/hooks/use-product-pricing';

interface OrderConstructionFormProps {
  product: ProductWithDetails;
  personalizedProduct: ProductWithDetails;
  selectedVariant: number | undefined;
  setSelectedVariant: (variant: number | undefined) => void;
  quantity: number;
  setQuantity: (quantity: number) => void;
  t: (key: string) => string;
}


export function OrderConstructionForm({
  product,
  personalizedProduct,
  selectedVariant,
  setSelectedVariant,
  quantity,
  setQuantity,
  t
}: OrderConstructionFormProps) {
  // Use centralized pricing logic for variant types
  const { variantTypes } = useProductPricing({
    product: personalizedProduct,
    selectedVariant,
    quantity
  });

  return (
    <div className="space-y-6">
      {/* Variants Selection */}
      {personalizedProduct.variants.length > 0 && (
        <div className="space-y-3">
          <Label className="text-base font-semibold">
            {variantTypes.length > 0 ? `${t('select')} ${variantTypes.join('/')}` : t('selectVariant')}
          </Label>
          <div className="grid grid-cols-2 gap-2">
            {personalizedProduct.variants.map((variant, index) => {
              const variantTranslation = variant.translations[0];
              const variantName =
                variantTranslation?.variant_name || variant.original_variant_name;
              const variantWithDisplay = variant as VariantWithDisplay;
              const variantPrice = Number(variantWithDisplay.display_price || variant.price_low);
              const variantCurrency = variantWithDisplay.display_currency || variant.currency;
              const variantId = variant.id || index;
              const isSelected = selectedVariant === variantId;

              return (
                <Button
                  key={variantId}
                  variant={isSelected ? "default" : "outline"}
                  className="justify-start text-left h-auto p-3"
                  onClick={() => setSelectedVariant(Number(variantId))}
                >
                  <div className="w-full">
                    <div className="font-medium text-sm">{variantName}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatCurrency(variantPrice, variantCurrency)}
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>
        </div>
      )}

      {/* Quantity Selector */}
      <div className="space-y-3">
        <Label className="text-base font-semibold">
          {t('quantity')}
        </Label>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            disabled={quantity <= 1}
          >
            -
          </Button>
          <Input
            type="number"
            min="1"
            value={quantity}
            onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
            className="w-24 text-center"
          />
          <Button
            variant="outline"
            size="icon"
            onClick={() => setQuantity(quantity + 1)}
          >
            +
          </Button>
        </div>
      </div>

      {/* Pricing Tiers */}
      {personalizedProduct.offers.length > 1 && (
        <div className="space-y-3">
          <Label className="text-base font-semibold">
            {t('pricingTiers')}
          </Label>
          <div className="space-y-2">
            {personalizedProduct.offers.map((offer, index) => {
              const offerWithDisplay = offer as OfferWithDisplay;
              const offerPrice = Number(offerWithDisplay.display_price || offer.price_low);
              const offerCurrency = offerWithDisplay.display_currency || offer.currency;
              const isSelected = quantity >= Number(offer.min_quantity || 1);

              return (
                <div
                  key={index}
                  className={`flex justify-between items-center p-3 rounded-lg ${
                    isSelected ? 'bg-primary/10 border border-primary/20' : 'bg-muted/30'
                  }`}
                >
                  <div className="flex flex-col">
                    <span className="font-medium text-sm">{offer.quantity_info}</span>
                    {offer.min_quantity && (
                      <span className="text-xs text-muted-foreground">
                        Min: {Number(offer.min_quantity)} {t('pieces')}
                      </span>
                    )}
                  </div>
                  <span className="text-lg font-semibold text-primary">
                    {formatCurrency(offerPrice, offerCurrency)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Services and Shipping Info */}
      <div className="space-y-3">
        <Label className="text-base font-semibold">
          {t('servicesAndShipping')}
        </Label>
        <div className="grid grid-cols-3 gap-2">
          <div className="flex flex-col items-center p-3 bg-muted/30 rounded-lg">
            <Truck className="h-5 w-5 text-primary mb-1" />
            <span className="text-xs text-center">{t('worldwideShipping')}</span>
          </div>
          <div className="flex flex-col items-center p-3 bg-muted/30 rounded-lg">
            <Shield className="h-5 w-5 text-primary mb-1" />
            <span className="text-xs text-center">{t('buyerProtection')}</span>
          </div>
          <div className="flex flex-col items-center p-3 bg-muted/30 rounded-lg">
            <Package className="h-5 w-5 text-primary mb-1" />
            <span className="text-xs text-center">{t('securePayment')}</span>
          </div>
        </div>
      </div>

      {/* Product Attributes */}
      {product.product_attributes.length > 0 && (
        <div className="space-y-3">
          <Label className="text-base font-semibold">
            {t('specifications')}
          </Label>
          <div className="grid grid-cols-2 gap-2">
            {product.product_attributes.slice(0, 6).map((attr, index) => {
              const attrTranslation = attr.translations[0];
              const key = attrTranslation?.attr_key || attr.original_attr_key;
              const value = attrTranslation?.attr_value || attr.original_attr_value;
              return (
                <div key={`${attr.original_attr_key}-${index}`} className="flex flex-col p-2 bg-muted/30 rounded-lg">
                  <span className="text-xs text-muted-foreground">{key}</span>
                  <span className="text-sm font-medium">{value}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}