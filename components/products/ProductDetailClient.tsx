'use client';

// components/products/ProductDetailClient.tsx
// Enhanced client component for product detail page with comprehensive information

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { 
  ShoppingCart, 
  Check, 
  ZoomIn, 
  Heart, 
  Truck, 
  Shield, 
  Package, 
  Info,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useCartStore } from '@/hooks/use-cart-store';
import { formatCurrency } from '@/lib/utils';
import { DEFAULT_CURRENCY, MARKETPLACES } from '@/lib/constants';
import type { ProductWithDetails } from '@/lib/types';
import { useRouter } from 'next/navigation';
import { trackProductView, trackCartAdd } from '@/lib/actions/user-activity.actions';
import { SocialShare } from './SocialShare';
import { ProductMediaDisplayClient } from './ProductMediaDisplayClient';
import { ProductAttributes } from './ProductAttributes';
import { ProductImagesGrid } from './ProductImagesGrid';
import { CategoryBadges } from './CategoryBadges';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { useProductPricing } from '@/hooks/use-product-pricing';
import { getPersonalizedProductPricing } from '@/lib/actions/client/product-client.actions';
import { OrderConstructionSheet } from './OrderConstructionSheet';
import { OrderConstructionForm } from './OrderConstructionForm';

interface ProductDetailClientProps {
  product: ProductWithDetails;
  locale: string;
}


export function ProductDetailClient({ product, locale }: ProductDetailClientProps) {
  const t = useTranslations('products');
  const addItem = useCartStore((state) => state.addItem);
  const { currency: userCurrency } = useCurrency();
  const router = useRouter();

  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<number | undefined>();
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);
  const [isZoomOpen, setIsZoomOpen] = useState(false);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isOrderSheetOpen, setIsOrderSheetOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // State for personalized pricing
  const [personalizedProduct, setPersonalizedProduct] = useState<ProductWithDetails>(product);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Track product view on component mount
  useEffect(() => {
    const trackView = async () => {
      try {
        await trackProductView(product.id, product.marketplace);
      } catch (error) {
        // Silently fail - activity tracking shouldn't break the UI
        console.error('Failed to track product view:', error);
      }
    };

    trackView();
  }, [product.id, product.marketplace]);

  // Fetch personalized pricing when currency changes
  useEffect(() => {
    const fetchPersonalizedPricing = async () => {
      // Only fetch if currency is different from default USD
      if (userCurrency !== DEFAULT_CURRENCY) {
        setIsLoadingPricing(true);
        try {
          const pricingData = await getPersonalizedProductPricing(product.id, userCurrency, locale);
          if (pricingData) {
            // Update the product with personalized pricing
            setPersonalizedProduct(prevProduct => ({
              ...prevProduct,
              offers: pricingData.offers,
              variants: pricingData.variants,
            }));
          }
        } catch (error) {
          console.error('Failed to fetch personalized pricing:', error);
        } finally {
          setIsLoadingPricing(false);
        }
      } else {
        // Reset to original product data for USD
        setPersonalizedProduct(product);
      }
    };

    fetchPersonalizedPricing();
  }, [userCurrency, product.id, locale, product]);

  // Get translations
  const translation = product.translations[0];
  const productName = translation?.name || product.original_name || 'Product';

  // Get images - separate preview/video from description images
  const galleryImages = product.product_images.filter(img => img.image_type !== 'description' && img.image_type !== 'video');
  const descriptionImages = product.product_images.filter(img => img.image_type === 'description');
  const images = galleryImages; // For backward compatibility in navigation logic
  const currentImage = images[selectedImage]?.image_url;
  const currentImageType = images[selectedImage]?.image_type;

  // Get categories
  const categories = product.categories;

  // Use centralized pricing logic
  const { price, currency, currentOffer, variantTypes } = useProductPricing({
    product: personalizedProduct,
    selectedVariant,
    quantity
  });

  const lowestOffer = personalizedProduct.offers[0];

  // Handle add to cart
  const handleAddToCart = async () => {
    setIsAdding(true);

    try {
      addItem({
        productId: product.id,
        variantId: selectedVariant,
        quantity,
      });

      // Track cart addition
      try {
        await trackCartAdd(product.id, product.marketplace);
      } catch (error) {
        console.error('Failed to track cart addition:', error);
      }

      // Show success feedback
      setTimeout(() => {
        setIsAdding(false);
      }, 1000);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setIsAdding(false);
    }
  };

  // Handle buy now (add to cart and redirect to checkout)
  const handleBuyNow = async () => {
    setIsAdding(true);

    try {
      addItem({
        productId: product.id,
        variantId: selectedVariant,
        quantity,
      });

      // Track cart addition
      try {
        await trackCartAdd(product.id, product.marketplace);
      } catch (error) {
        console.error('Failed to track cart addition:', error);
      }

      // Redirect to checkout
      router.push(`/${locale}/checkout/shipping`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setIsAdding(false);
    }
  };

  // Handle wishlist toggle
  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted);
    // TODO: Implement wishlist functionality
  };

  // Navigate images
  const handlePrevImage = () => {
    setSelectedImage((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNextImage = () => {
    setSelectedImage((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  // Generate structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: productName,
    description: product.product_attributes.map(attr => {
      const attrTranslation = attr.translations[0];
      const key = attrTranslation?.attr_key || attr.original_attr_key;
      const value = attrTranslation?.attr_value || attr.original_attr_value;
      return `${key}: ${value}`;
    }).join(', '),
    image: product.product_images.map(img => img.image_url),
    offers: {
      "@type": "Offer",
      price: price.toString(),
      priceCurrency: currency,
      availability: "https://schema.org/InStock",
      seller: {
        "@type": "Organization",
        name: MARKETPLACES[product.marketplace]?.name || product.marketplace
      },
      priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    },
    category: categories.map(cat => {
      const categoryTranslation = cat.category.translations[0];
      return categoryTranslation?.name || 'Category';
    }).join(', '),
    brand: {
      "@type": "Brand",
      name: MARKETPLACES[product.marketplace]?.name || product.marketplace
    },
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      <div className="flex flex-col lg:flex-row gap-8 lg:gap-12">
        {/* Image Gallery Section */}
        <div className="space-y-4 lg:sticky lg:top-0 lg:h-screen lg:w-1/2">
          {/* Main Image with Zoom */}
          <div className="relative">
            <div className={`relative ${currentImageType === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden rounded-lg bg-muted border`}>
              {currentImage ? (
                <ProductMediaDisplayClient
                  src={currentImage}
                  alt={productName}
                  type={currentImageType || 'preview'}
                  className="object-cover"
                  priority={true}
                />
              ) : (
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  {t('noImage')}
                </div>
              )}

              {/* Image Navigation Arrows */}
              {images.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute left-2 top-1/2 -translate-y-1/2 opacity-75 hover:opacity-100"
                    onClick={handlePrevImage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 opacity-75 hover:opacity-100"
                    onClick={handleNextImage}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Zoom Button */}
              {currentImageType !== 'video' && (
                <Dialog open={isZoomOpen} onOpenChange={setIsZoomOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="secondary"
                      size="icon"
                      className="absolute bottom-2 right-2 opacity-75 hover:opacity-100"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl w-full p-0">
                    <div className="relative aspect-square w-full">
                      {currentImage && (
                        <Image
                          src={currentImage}
                          alt={productName}
                          fill
                          className="object-contain"
                          sizes="100vw"
                        />
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              )}

              {/* Image Counter */}
               {images.length > 1 && (
                 <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                   {t('imageCounter', { current: selectedImage + 1, total: images.length })}
                 </div>
               )}
            </div>
          </div>

          {/* Thumbnail Gallery */}
          {images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative ${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden rounded-md border-2 transition-colors flex-shrink-0 w-24 ${
                    selectedImage === index
                      ? 'border-primary'
                      : 'border-transparent hover:border-muted-foreground'
                  }`}
                >
                  <ProductMediaDisplayClient
                    src={image.image_url}
                    alt={`${productName} ${index + 1}`}
                    type={image.image_type}
                    className="object-cover"
                    sizes="100px"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info Section */}
        <div className="space-y-6 lg:w-1/2 lg:overflow-y-auto">
          {/* Breadcrumb / Categories */}
          <CategoryBadges categories={categories} />

          {/* Title and Marketplace */}
          <div>
            <h1 className="text-3xl font-bold mb-3">{productName}</h1>
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="text-sm">
                {MARKETPLACES[product.marketplace]?.icon}{' '}
                {MARKETPLACES[product.marketplace]?.name}
              </Badge>
              {product.weight && (
                <Badge variant="outline" className="text-sm">
                  <Package className="h-3 w-3 mr-1" />
                  {Number(product.weight).toFixed(2)} {product.weight_unit || t('weightUnitFallback')}
                </Badge>
              )}
            </div>
          </div>

          {/* Price Section */}
          <div className="mt-4">
            <span className="text-2xl font-bold text-primary">
              {formatCurrency(price, currency)}
            </span>
            {lowestOffer && Number(lowestOffer.min_quantity) > 1 && (
              <p className="text-sm text-muted-foreground mt-1">
                {t('minOrder')}: {lowestOffer.min_quantity.toString()} {t('pieces')}
              </p>
            )}
          </div>

          {/* Desktop-only order construction */}
          {!isMobile && (
            <>
              <OrderConstructionForm
                product={product}
                personalizedProduct={personalizedProduct}
                selectedVariant={selectedVariant}
                setSelectedVariant={setSelectedVariant}
                quantity={quantity}
                setQuantity={setQuantity}
                t={t}
              />

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  size="lg"
                  variant="outline"
                  className="flex-1"
                  onClick={handleAddToCart}
                  disabled={isAdding}
                >
                  {isAdding ? (
                    <>
                      <Check className="mr-2 h-5 w-5" />
                      {t('addedToCart')}
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      {t('addToCart')}
                    </>
                  )}
                </Button>
                <Button
                  size="lg"
                  className="flex-1"
                  onClick={handleBuyNow}
                  disabled={isAdding}
                >
                  {t('buyNow')}
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  onClick={handleWishlistToggle}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current text-red-500' : ''}`} />
                </Button>
              </div>
            </>
          )}

          {/* Social Share */}
          <SocialShare product={product} locale={locale} price={price} currency={currency} />
        </div>
      </div>

      {/* Product Details Tabs */}
      <div className="mt-12 lg:mt-16">
        {isMobile ? (
          <div className="space-y-6">

            {/* Specifications Section */}
            <Card>
              <CardHeader>
                <CardTitle>{t('productSpecifications')}</CardTitle>
              </CardHeader>
              <CardContent>
                <ProductAttributes attributes={product.product_attributes} layout="list" />
              </CardContent>
            </Card>

            {/* Description Images Section */}
            <Card>
              <CardHeader>
                <CardTitle>{t('additionalDescriptionImages')}</CardTitle>
              </CardHeader>
              <CardContent>
                <ProductImagesGrid images={descriptionImages} productName={productName} layout="list" />
              </CardContent>
            </Card>
          </div>
        ) : (
          <Tabs defaultValue="specifications" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="specifications">{t('specifications')}</TabsTrigger>
              <TabsTrigger value="description">{t('descriptionImages')}</TabsTrigger>
            </TabsList>

            {/* Specifications Tab */}
            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('productSpecifications')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ProductAttributes attributes={product.product_attributes} layout="grid" />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Description Images Tab */}
            <TabsContent value="description" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>{t('additionalDescriptionImages')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ProductImagesGrid images={descriptionImages} productName={productName} layout="grid" />
                  <Separator />
                  <Separator />
                </CardContent>
              </Card>
            </TabsContent>

          </Tabs>
        )}
      </div>

      {/* Mobile Order Construction Button */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4 z-40">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">{t('total')}</p>
              <p className="text-xl font-bold text-primary">{formatCurrency(price * quantity, currency)}</p>
            </div>
            <Button
              size="lg"
              onClick={() => setIsOrderSheetOpen(true)}
              className="flex-1 ml-4"
            >
              <ShoppingCart className="mr-2 h-5 w-5" />
              {t('constructOrder')}
            </Button>
          </div>
        </div>
      )}

      {/* Mobile Order Construction Sheet */}
      {isMobile && (
        <OrderConstructionSheet
          product={product}
          locale={locale}
          isOpen={isOrderSheetOpen}
          onOpenChange={setIsOrderSheetOpen}
          showBuyNow={true}
          showWishlist={true}
          onBuyNow={handleBuyNow}
          onWishlistToggle={handleWishlistToggle}
          isWishlisted={isWishlisted}
          isAdding={isAdding}
        />
      )}
    </>
  );
}