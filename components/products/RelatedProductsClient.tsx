'use client';

// components/products/RelatedProductsClient.tsx
// Client component for related products with personalized pricing

import { useState, useEffect } from 'react';
// import { useTranslations } from 'next-intl';
import { ProductCard } from './ProductCard';
import { PricingProvider, type PricingData } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { getBatchProductPricing } from '@/lib/actions/client/product-client.actions';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductListItem } from '@/lib/types';

interface RelatedProductsClientProps {
  products: ProductListItem[];
  locale: string;
}

export function RelatedProductsClient({ products, locale }: RelatedProductsClientProps) {
//   const t = useTranslations('products');
  const { currency: userCurrency } = useCurrency();

  const [personalizedPricing, setPersonalizedPricing] = useState<Record<number, PricingData> | null>(null);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);

  // Fetch batch pricing when component mounts or currency changes
  useEffect(() => {
    const fetchBatchPricing = async () => {
      if (!userCurrency || userCurrency === DEFAULT_CURRENCY) return;

      setIsLoadingPricing(true);
      try {
        const productIds = products.map(p => p.id);
        const pricing = await getBatchProductPricing(productIds, userCurrency, locale);
        setPersonalizedPricing(pricing);
      } catch (error) {
        console.error('Failed to fetch batch pricing:', error);
      } finally {
        setIsLoadingPricing(false);
      }
    };

    fetchBatchPricing();
  }, [userCurrency, products, locale]);

  return (
    <PricingProvider pricing={personalizedPricing} isLoading={isLoadingPricing}>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            locale={locale}
          />
        ))}
      </div>
    </PricingProvider>
  );
}