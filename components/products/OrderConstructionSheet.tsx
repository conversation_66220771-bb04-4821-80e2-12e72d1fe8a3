// components/products/OrderConstructionSheet.tsx
'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import {
  ShoppingCart,
  Check,
  Package,
  Truck,
  Shield,
  Heart
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { useCartStore } from '@/hooks/use-cart-store';
import { formatCurrency } from '@/lib/utils';
import { DEFAULT_CURRENCY, MARKETPLACES } from '@/lib/constants';
import type { ProductWithDetails } from '@/lib/types';
import { trackCartAdd } from '@/lib/actions/user-activity.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { getPersonalizedProductPricing } from '@/lib/actions/client/product-client.actions';
import { useProductPricing } from '@/hooks/use-product-pricing';
import { OrderConstructionForm } from './OrderConstructionForm';

interface OrderConstructionSheetProps {
  product: ProductWithDetails;
  locale: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  showBuyNow?: boolean;
  showWishlist?: boolean;
  onBuyNow?: () => void;
  onWishlistToggle?: () => void;
  isWishlisted?: boolean;
  isAdding?: boolean;
}



export function OrderConstructionSheet({
  product,
  locale,
  isOpen,
  onOpenChange,
  showBuyNow = false,
  showWishlist = false,
  onBuyNow,
  onWishlistToggle,
  isWishlisted = false,
  isAdding = false
}: OrderConstructionSheetProps) {
  const t = useTranslations('products');
  const addItem = useCartStore((state) => state.addItem);
  const { currency: userCurrency } = useCurrency();

  const [selectedVariant, setSelectedVariant] = useState<number | undefined>();
  const [quantity, setQuantity] = useState(1);
  const [localIsAdding, setLocalIsAdding] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);

  // State for personalized pricing
  const [personalizedProduct, setPersonalizedProduct] = useState<ProductWithDetails>(product);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);

  // Fetch personalized pricing when currency changes
  useEffect(() => {
    const fetchPersonalizedPricing = async () => {
      // Only fetch if currency is different from default USD
      if (userCurrency !== DEFAULT_CURRENCY) {
        setIsLoadingPricing(true);
        try {
          const pricingData = await getPersonalizedProductPricing(product.id, userCurrency, locale);
          if (pricingData) {
            // Update the product with personalized pricing
            setPersonalizedProduct(prevProduct => ({
              ...prevProduct,
              offers: pricingData.offers,
              variants: pricingData.variants,
            }));
          }
        } catch (error) {
          console.error('Failed to fetch personalized pricing:', error);
        } finally {
          setIsLoadingPricing(false);
        }
      } else {
        // Reset to original product data for USD
        setPersonalizedProduct(product);
      }
    };

    fetchPersonalizedPricing();
  }, [userCurrency, product.id, locale, product]);

  // Get translations
  const translation = product.translations[0];
  const productName = translation?.name || product.original_name || 'Product';

  // Get images - separate preview/video from description images
  const galleryImages = product.product_images.filter(img => img.image_type !== 'description' && img.image_type !== 'video');
  const images = galleryImages;
  const currentImage = images[selectedImage]?.image_url;
  const currentImageType = images[selectedImage]?.image_type;

  // Use centralized pricing logic
  const { price, currency, currentOffer, variantTypes, getOfferForQuantity } = useProductPricing({
    product: personalizedProduct,
    selectedVariant,
    quantity
  });

  const lowestOffer = personalizedProduct.offers[0];

  // Handle add to cart
  const handleAddToCart = async () => {
    setLocalIsAdding(true);

    try {
      addItem({
        productId: product.id,
        variantId: selectedVariant,
        quantity,
      });

      // Track cart addition
      try {
        await trackCartAdd(product.id, product.marketplace);
      } catch (error) {
        console.error('Failed to track cart addition:', error);
      }

      // Show success feedback
      setTimeout(() => {
        setLocalIsAdding(false);
        onOpenChange(false); // Close the sheet after adding to cart
      }, 1000);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setLocalIsAdding(false);
    }
  };

  // Navigate images
  // const handlePrevImage = () => {
  //   setSelectedImage((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  // };

  // const handleNextImage = () => {
  //   setSelectedImage((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  // };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="h-[85vh] py-1 px-4 overflow-y-auto rounded-t-xl">
        <SheetHeader className="text-left">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg font-normal">{t('constructOrder')}</SheetTitle>
          </div>
        </SheetHeader>
        
        <div className="mt-4 space-y-6">
          {/* Product Image and Basic Info - Always Visible */}
          <div className="sticky top-0 bg-background z-10 pb-4 border-b">
            <div className="flex gap-4">
              <div className="relative w-24 h-24 flex-shrink-0">
                <div className={`relative ${currentImageType === 'video' ? 'aspect-video' : 'aspect-square'} overflow-hidden rounded-lg bg-muted border`}>
                  {currentImage ? (
                    <img
                      src={currentImage}
                      alt={productName}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center text-muted-foreground">
                      {t('noImage')}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-sm line-clamp-2">{productName}</h3>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {MARKETPLACES[product.marketplace]?.icon}{' '}
                    {MARKETPLACES[product.marketplace]?.name}
                  </Badge>
                  {product.weight && (
                    <Badge variant="outline" className="text-xs">
                      <Package className="h-3 w-3 mr-1" />
                      {Number(product.weight).toFixed(2)} {product.weight_unit || 'kg'}
                    </Badge>
                  )}
                </div>
                <div className="mt-2">
                  <span className="text-lg font-bold text-primary">
                    {formatCurrency(price * quantity, currency)}
                  </span>
                  <p className="text-xs text-muted-foreground">
                    {quantity} {t('pieces')} × {formatCurrency(price, currency)} {t('each')}
                  </p>
                  {lowestOffer && Number(lowestOffer.min_quantity) > 1 && (
                    <p className="text-xs text-muted-foreground">
                      {t('minOrder')}: {lowestOffer.min_quantity.toString()} {t('pieces')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Image Gallery */}
          {images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto pb-2">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative ${image.image_type === 'video' ? 'aspect-video' : 'aspect-square'} w-16 h-16 overflow-hidden rounded-md border-2 flex-shrink-0 transition-colors ${
                    selectedImage === index
                      ? 'border-primary'
                      : 'border-transparent hover:border-muted-foreground'
                  }`}
                >
                  <img
                    src={image.image_url}
                    alt={`${productName} ${index + 1}`}
                    className="object-cover w-full h-full"
                  />
                </button>
              ))}
            </div>
          )}

          <OrderConstructionForm
            product={product}
            personalizedProduct={personalizedProduct}
            selectedVariant={selectedVariant}
            setSelectedVariant={setSelectedVariant}
            quantity={quantity}
            setQuantity={setQuantity}
            t={t}
          />

          {/* Action Buttons */}
          <div className="sticky bottom-0 bg-background pt-2 pb-4">
            {showBuyNow ? (
              <div className="flex gap-3">
                <Button
                  size="lg"
                  variant="outline"
                  className="flex-1"
                  onClick={handleAddToCart}
                  disabled={localIsAdding}
                >
                  {localIsAdding ? (
                    <>
                      <Check className="mr-2 h-5 w-5" />
                      {t('addedToCart')}
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-5 w-5" />
                      {t('addToCart')}
                    </>
                  )}
                </Button>
                <Button
                  size="lg"
                  className="flex-1"
                  onClick={onBuyNow}
                  disabled={isAdding}
                >
                  {t('buyNow')}
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  onClick={onWishlistToggle}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current text-red-500' : ''}`} />
                </Button>
              </div>
            ) : (
              <Button
                size="lg"
                className="w-full"
                onClick={handleAddToCart}
                disabled={localIsAdding}
              >
                {localIsAdding ? (
                  <>
                    <Check className="mr-2 h-5 w-5" />
                    {t('addedToCart')}
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    {t('addToCart')} - {formatCurrency(price * quantity, currency)}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}