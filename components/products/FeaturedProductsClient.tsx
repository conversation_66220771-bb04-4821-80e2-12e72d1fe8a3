'use client';

// components/products/FeaturedProductsClient.tsx
// Client component for featured products with batch pricing

import { useState, useEffect, useTransition } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProductCard } from './ProductCard';
import { PricingProvider, type PricingData } from '@/components/providers/PricingProvider';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import { getBatchProductPricing } from '@/lib/actions/client/product-client.actions';
import { DEFAULT_CURRENCY } from '@/lib/constants';
import type { ProductListItem } from '@/lib/types';

interface FeaturedProductsClientProps {
  products: ProductListItem[];
  locale: string;
}

export function FeaturedProductsClient({ products, locale }: FeaturedProductsClientProps) {
  const t = useTranslations('home');
  const [isPending, startTransition] = useTransition();
  const { currency: userCurrency } = useCurrency();

  const [personalizedPricing, setPersonalizedPricing] = useState<Record<number, PricingData> | null>(null);
  const [isLoadingPricing, setIsLoadingPricing] = useState(false);

  // Fetch batch pricing when component mounts or currency changes
  useEffect(() => {
    const fetchBatchPricing = async () => {
      if (!userCurrency || userCurrency === DEFAULT_CURRENCY) return;

      setIsLoadingPricing(true);
      try {
        const productIds = products.map(p => p.id);
        const pricing = await getBatchProductPricing(productIds, userCurrency);
        setPersonalizedPricing(pricing);
      } catch (error) {
        console.error('Failed to fetch batch pricing:', error);
      } finally {
        setIsLoadingPricing(false);
      }
    };

    fetchBatchPricing();
  }, [userCurrency, products]);

  return (
    <PricingProvider pricing={personalizedPricing} isLoading={isLoadingPricing}>
      <section className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold">{t('featuredProducts')}</h2>
            <p className="text-muted-foreground mt-2">
              {t('featuredProductsDescription')}
            </p>
          </div>
          <Link href={`/${locale}/products`}>
            <Button variant="outline">
              {t('viewAll')}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              locale={locale}
            />
          ))}
        </div>
      </section>
    </PricingProvider>
  );
}