'use client';

// components/providers/index.tsx
// Compose all providers together

import { ThemeProvider } from './ThemeProvider';
import { FirebaseAuthProvider } from './FirebaseAuthProvider';
import { CurrencyProvider } from './CurrencyProvider';
import { LoadingProvider } from './LoadingProvider';

interface ProvidersProps {
  children: React.ReactNode;
  initialCurrency?: string;
}

export function Providers({ children, initialCurrency }: ProvidersProps) {
  return (
    <ThemeProvider>
      <FirebaseAuthProvider>
        <CurrencyProvider initialCurrency={initialCurrency}>
          <LoadingProvider>
            {children}
          </LoadingProvider>
        </CurrencyProvider>
      </FirebaseAuthProvider>
    </ThemeProvider>
  );
}
