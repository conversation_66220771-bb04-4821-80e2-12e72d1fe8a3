'use client';

import React, { createContext, useContext, ReactNode } from 'react';

export interface PricingData {
  offers: Array<{
    id: bigint;
    min_quantity: number | null;
    price_low: number;
    price_high: number | null;
    currency: string;
    quantity_info: string | null;
    display_price?: number;
    display_currency?: string;
    exchange_rate?: number;
  }>;
  variants: Array<{
    id: bigint;
    original_variant_name: string;
    original_variant_type: string;
    price_low: number;
    price_high: number | null;
    currency: string;
    available_quantity: number | null;
    min_quantity: number | null;
    display_price?: number;
    display_currency?: string;
    exchange_rate?: number;
  }>;
}

interface PricingContextType {
  getPricing: (productId: number) => PricingData | null;
  isLoading: boolean;
}

const PricingContext = createContext<PricingContextType | undefined>(undefined);

interface PricingProviderProps {
  children: ReactNode;
  pricing: Record<number, PricingData> | null;
  isLoading?: boolean;
}

export function PricingProvider({ children, pricing, isLoading = false }: PricingProviderProps) {
  const getPricing = (productId: number): PricingData | null => {
    if (!pricing || !pricing[productId]) {
      return null;
    }
    return pricing[productId];
  };

  return (
    <PricingContext.Provider value={{ getPricing, isLoading }}>
      {children}
    </PricingContext.Provider>
  );
}

export function usePricing() {
  const context = useContext(PricingContext);
  if (context === undefined) {
    throw new Error('usePricing must be used within a PricingProvider');
  }
  return context;
}