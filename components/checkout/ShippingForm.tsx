'use client';

// components/checkout/ShippingForm.tsx
// Shipping address selection and form

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCartStore } from '@/hooks/use-cart-store';
import { createOrder } from '@/lib/actions/order.actions';
import { useCurrency } from '@/components/providers/CurrencyProvider';
import type { Address } from '@/lib/types';

interface ShippingFormProps {
  addresses: Address[];
  locale: string;
}

export function ShippingForm({ addresses, locale }: ShippingFormProps) {
   const t = useTranslations('checkout');
   const router = useRouter();
   const cartData = useCartStore((state) => state.data);
   const items = cartData?.items || [];
   const clearCart = useCartStore((state) => state.clearCart);
   const { currency } = useCurrency();

  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(
    addresses.length > 0 ? addresses[0].id : null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!selectedAddressId) {
      setError(t('selectAddressError'));
      return;
    }

    if (items.length === 0) {
      setError(t('emptyCartError'));
      return;
    }

    setIsProcessing(true);

    try {
      const selectedAddress = addresses.find((addr) => addr.id === selectedAddressId);

      if (!selectedAddress) {
        throw new Error('Address not found');
      }

      // Get exchange rate from CNY to user's currency
      let exchangeRate = 1;
      try {
        const response = await fetch(`/api/exchange-rate?from=CNY&to=${currency}`);
        const data = await response.json();
        exchangeRate = data.rate || 1;
      } catch (error) {
        console.error('Failed to get exchange rate:', error);
      }

      // Create order with server-side price calculation
      const result = await createOrder({
        items,
        shippingAddress: selectedAddress,
        userCurrency: currency,
        shippingCost: 0, // TODO: Calculate shipping cost
      });

      if (result.success && result.orderId) {
        // Clear cart
        clearCart();

        // Redirect to success page
        router.push(`/${locale}/checkout/success/${result.orderId}`);
      } else {
        setError(result.error || t('orderError'));
      }
    } catch (err) {
      console.error('Checkout error:', err);
      setError(err instanceof Error ? err.message : t('orderError'));
    } finally {
      setIsProcessing(false);
    }
  };

  if (addresses.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground mb-4">
            {t('noAddressesFound')}
          </p>
          <p className="text-center text-sm text-muted-foreground">
            {t('addAddressInProfile')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      {/* Address Selection */}
      <div className="space-y-4">
        <Label>{t('selectAddress')}</Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <Card
              key={address.id}
              className={`cursor-pointer transition-colors ${
                selectedAddressId === address.id
                  ? 'border-primary ring-2 ring-primary'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => setSelectedAddressId(address.id)}
            >
              <CardContent className="pt-6">
                <div className="space-y-1">
                  <p className="font-semibold">{address.fullName}</p>
                  <p className="text-sm text-muted-foreground">{address.phone}</p>
                  <p className="text-sm">{address.addressLine1}</p>
                  {address.addressLine2 && (
                    <p className="text-sm">{address.addressLine2}</p>
                  )}
                  <p className="text-sm">
                    {address.city}, {address.state} {address.postalCode}
                  </p>
                  <p className="text-sm">{address.country}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" size="lg" disabled={isProcessing || !selectedAddressId}>
          {isProcessing ? t('processing') : t('placeOrder')}
        </Button>
      </div>
    </form>
  );
}

