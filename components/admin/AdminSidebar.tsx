'use client';

// components/admin/AdminSidebar.tsx
// Admin sidebar navigation

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import {
  LayoutDashboard,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  Warehouse,
  CreditCard,
  Settings,
  FolderTree,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AdminSidebarProps {
  locale: string;
}

export function AdminSidebar({ locale }: AdminSidebarProps) {
  const t = useTranslations('admin');
  const pathname = usePathname();

  const navigation = [
    {
      name: t('dashboard'),
      href: `/${locale}/admin`,
      icon: LayoutDashboard,
    },
    {
      name: t('orders'),
      href: `/${locale}/admin/orders`,
      icon: ShoppingCart,
    },
    {
      name: t('products'),
      href: `/${locale}/admin/products`,
      icon: Package,
    },
    {
      name: t('categories'),
      href: `/${locale}/admin/categories`,
      icon: FolderTree,
    },
    {
      name: t('customers'),
      href: `/${locale}/admin/customers`,
      icon: Users,
    },
    {
      name: t('warehouse'),
      href: `/${locale}/admin/warehouse`,
      icon: Warehouse,
    },
    {
      name: t('payments'),
      href: `/${locale}/admin/payments`,
      icon: CreditCard,
    },
    {
      name: t('pricing'),
      href: `/${locale}/admin/pricing`,
      icon: DollarSign,
    },
  ];

  return (
    <aside className="w-64 bg-card border-r min-h-screen">
      <div className="p-6">
        <h2 className="text-2xl font-bold">MaoMao Admin</h2>
      </div>

      <nav className="px-4 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/');

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center gap-3 px-4 py-3 rounded-lg transition-colors',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-accent hover:text-accent-foreground'
              )}
            >
              <Icon className="h-5 w-5" />
              <span className="font-medium">{item.name}</span>
            </Link>
          );
        })}
      </nav>
    </aside>
  );
}

