'use client';

// components/admin/CategoryForm.tsx
// Category form for create and edit operations

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, Trash2, Save } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { createCategory, updateCategory } from '@/lib/actions/admin/category.actions';
import type { CategoryWithTranslations } from '@/lib/actions/admin/category.actions';
import type { SuccessResponseWithId } from '@/lib/types/admin';

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'fr', name: 'Français' },
  { code: 'ar', name: 'العربية' },
  { code: 'zh', name: '中文' },
];

interface CategoryFormData {
  parent_id?: number;
  translations: Array<{
    language_code: string;
    name: string;
    slug: string;
    description?: string;
  }>;
}

interface CategoryFormProps {
  mode: 'create' | 'edit';
  category?: CategoryWithTranslations;
  parentId?: number;
  availableParents: CategoryWithTranslations[];
  locale: string;
}

export function CategoryForm({ 
  mode, 
  category, 
  parentId, 
  availableParents, 
  locale 
}: CategoryFormProps) {
  const t = useTranslations('admin');
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<CategoryFormData>(() => {
    if (mode === 'edit' && category) {
      return {
        parent_id: category.parent_id || undefined,
        translations: category.translations.map(t => ({
          language_code: t.language_code,
          name: t.name,
          slug: t.slug,
          description: t.description || undefined,
        })),
      };
    }

    return {
      parent_id: parentId,
      translations: [
        {
          language_code: locale,
          name: '',
          slug: '',
          description: '',
        },
      ],
    };
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (formData.translations.length === 0) {
        setError(t('atLeastOneTranslationRequired'));
        return;
      }

      const hasValidTranslation = formData.translations.some(t => t.name.trim() && t.slug.trim());
      if (!hasValidTranslation) {
        setError(t('atLeastOneCompleteTranslationRequired'));
        return;
      }

      // Filter out empty translations
      const validTranslations = formData.translations.filter(t => t.name.trim() && t.slug.trim());

      if (mode === 'create') {
        const result = await createCategory({
          parent_id: formData.parent_id,
          translations: validTranslations,
        });

        if (result.success) {
          router.push(`/${locale}/admin/categories/${result.categoryId}`);
        } else {
          setError(result.error || 'An error occurred');
        }
      } else {
        const result = await updateCategory(category!.id, {
          parent_id: formData.parent_id,
          translations: validTranslations,
        });

        if (result.success) {
          router.push(`/${locale}/admin/categories/${category!.id}`);
        } else {
          setError(result.error || 'An error occurred');
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const addTranslation = () => {
    setFormData(prev => ({
      ...prev,
      translations: [
        ...prev.translations,
        {
          language_code: 'en',
          name: '',
          slug: '',
          description: '',
        },
      ],
    }));
  };

  const removeTranslation = (index: number) => {
    setFormData(prev => ({
      ...prev,
      translations: prev.translations.filter((_, i) => i !== index),
    }));
  };

  const updateTranslation = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      translations: prev.translations.map((translation, i) => 
        i === index ? { ...translation, [field]: value } : translation
      ),
    }));
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const getCategoryName = (category: CategoryWithTranslations) => {
    const translation = category.translations.find(t => t.language_code === locale) ||
                       category.translations.find(t => t.language_code === 'en') ||
                       category.translations[0];
    return translation?.name || `Category ${category.id}`;
  };

  // Filter out current category and its descendants from parent options
  const getAvailableParents = () => {
    if (mode === 'create') return availableParents;
    
    const excludeIds = new Set([category!.id]);
    const addDescendants = (catId: number) => {
      availableParents.forEach(cat => {
        if (cat.parent_id === catId) {
          excludeIds.add(cat.id);
          addDescendants(cat.id);
        }
      });
    };
    addDescendants(category!.id);
    
    return availableParents.filter(cat => !excludeIds.has(cat.id));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">
          {error}
        </div>
      )}

      {/* Parent Category Selection */}
      <div className="space-y-2">
        <Label htmlFor="parent_id">{t('parentCategory')}</Label>
        <Select
          value={formData.parent_id?.toString() || ''}
          onValueChange={(value) => setFormData(prev => ({ 
            ...prev, 
            parent_id: value ? parseInt(value) : undefined 
          }))}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('selectParentCategory')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">{t('noParent')} ({t('rootCategory')})</SelectItem>
            {getAvailableParents().map((parent) => (
              <SelectItem key={parent.id} value={parent.id.toString()}>
                {getCategoryName(parent)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Translations */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">{t('categoryTranslations')}</h3>
          <Button type="button" variant="outline" size="sm" onClick={addTranslation}>
            <Plus className="h-4 w-4 mr-2" />
            {t('addTranslation')}
          </Button>
        </div>

        <div className="space-y-4">
          {formData.translations.map((translation, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between">
                <Badge variant="outline">{translation.language_code}</Badge>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeTranslation(index)}
                  disabled={formData.translations.length === 1}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>{t('language')}</Label>
                    <Select
                      value={translation.language_code}
                      onValueChange={(value) => updateTranslation(index, 'language_code', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {LANGUAGES.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>{t('name')} *</Label>
                    <Input
                      value={translation.name}
                      onChange={(e) => {
                        updateTranslation(index, 'name', e.target.value);
                        // Auto-generate slug if empty
                        if (!translation.slug) {
                          updateTranslation(index, 'slug', generateSlug(e.target.value));
                        }
                      }}
                      placeholder={t('categoryName')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>{t('slug')} *</Label>
                    <Input
                      value={translation.slug}
                      onChange={(e) => updateTranslation(index, 'slug', e.target.value)}
                      placeholder="category-slug"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>{t('description')}</Label>
                  <Textarea
                    value={translation.description || ''}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateTranslation(index, 'description', e.target.value)}
                    placeholder={t('categoryDescription')}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex gap-4 pt-6 border-t">
        <Button type="submit" disabled={loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? t('saving') : mode === 'create' ? t('createCategory') : t('updateCategory')}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={loading}
        >
          {t('cancel')}
        </Button>
      </div>
    </form>
  );
}
